# OpenDeepWiki Python API Dependencies

# Web Framework
fastapi>=0.95.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# Database
sqlalchemy>=2.0.23
alembic>=1.12.1
aiosqlite>=0.19.0
asyncpg>=0.29.0  # PostgreSQL
aiomysql>=0.2.0  # MySQL

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Configuration & Environment
pydantic[email]>=2.5.0
pydantic-settings>=2.1.0
python-dotenv>=1.0.0
email-validator>=2.0.0

# AI & ML
openai>=1.3.0
anthropic>=0.7.0
# semantic-kernel==0.9.6b1  # Commented out - not compatible with Python 3.13
# sentence-transformers>=2.2.2  # Commented out - heavy dependency
# chromadb>=0.4.18  # Commented out - heavy dependency
tiktoken>=0.5.2

# Git Integration
GitPython>=3.1.40
PyGithub>=2.1.1
python-gitlab>=4.2.0

# Background Tasks & Queuing
celery>=5.3.4
redis>=5.0.1
rq>=1.15.1

# HTTP Client
httpx>=0.25.2
aiohttp>=3.9.1

# Data Processing
pandas>=2.1.4
numpy>=1.24.4
pydantic>=2.5.0

# Logging & Monitoring
loguru>=0.7.2
prometheus-client>=0.19.0

# File Processing
python-magic>=0.4.27
Pillow>=10.1.0
pypdf>=3.17.1
python-docx>=1.1.0

# Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
httpx>=0.25.2  # for testing FastAPI

# Development
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.1

# Documentation
mkdocs>=1.5.3
mkdocs-material>=9.4.8

# Utilities
click>=8.1.7
rich>=13.7.0
typer>=0.9.0
