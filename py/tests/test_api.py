"""
API endpoint tests
"""

import pytest
from httpx import Async<PERSON>lient
from fastapi.testclient import TestClient

from main import app


@pytest.fixture
def client():
    """Test client fixture"""
    return TestClient(app)


@pytest.mark.asyncio
async def test_root_endpoint():
    """Test root endpoint"""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        response = await ac.get("/")
        assert response.status_code == 200
        assert "OpenDeepWiki Python API" in response.json()["message"]


@pytest.mark.asyncio
async def test_health_endpoint():
    """Test health check endpoint"""
    async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
        response = await ac.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"


@pytest.mark.asyncio
async def test_api_docs():
    """Test API documentation endpoint"""
    async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/docs")
        assert response.status_code == 200


class TestAuthAPI:
    """Authentication API tests"""
    
    @pytest.mark.asyncio
    async def test_register_user(self):
        """Test user registration"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post("/api/auth/register", json={
                "name": "testuser",
                "email": "<EMAIL>",
                "password": "testpass123"
            })
            # Note: This will fail without proper database setup
            # In real tests, you'd use a test database
            assert response.status_code in [200, 422, 500]


class TestWarehouseAPI:
    """Warehouse API tests"""
    
    @pytest.mark.asyncio
    async def test_list_warehouses_unauthorized(self):
        """Test listing warehouses without authentication"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/warehouse/")
            assert response.status_code == 401


class TestSystemAPI:
    """System API tests"""
    
    @pytest.mark.asyncio
    async def test_system_health(self):
        """Test system health endpoint"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/system/health")
            assert response.status_code == 200
            data = response.json()
            assert "status" in data
            assert "timestamp" in data
