#!/usr/bin/env python3
"""
Database migration script
"""

import subprocess
import sys
import os


def run_command(command, cwd=None):
    """Run shell command"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            cwd=cwd
        )
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        return False


def main():
    """Main migration function"""
    
    # Change to project directory
    project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    print("Running database migrations...")
    
    # Initialize alembic if needed
    if not os.path.exists(os.path.join(project_dir, "alembic")):
        print("Initializing Alembic...")
        if not run_command("alembic init alembic", cwd=project_dir):
            sys.exit(1)
    
    # Generate migration
    print("Generating migration...")
    if not run_command("alembic revision --autogenerate -m 'Initial migration'", cwd=project_dir):
        print("Warning: Could not generate migration (this is normal if no changes)")
    
    # Apply migrations
    print("Applying migrations...")
    if not run_command("alembic upgrade head", cwd=project_dir):
        sys.exit(1)
    
    print("Migrations completed successfully!")


if __name__ == "__main__":
    main()
