#!/usr/bin/env python3
"""
Database initialization script
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# Add src directory to path
src_path = os.path.join(os.path.dirname(__file__), '..', 'src')
sys.path.insert(0, src_path)

from core.database import init_database, get_database_session
from core.auth import get_password_hash
from models.user import User, Role
from models.system import SystemSetting

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def init_default_data():
    """Initialize default data"""
    
    try:
        # Initialize database
        await init_database()
        logger.info("Database initialized")
        
        async with get_database_session() as db:
            # Create default roles
            admin_role = Role(
                id="role_admin",
                name="admin",
                description="Administrator role with full access"
            )
            user_role = Role(
                id="role_user", 
                name="user",
                description="Regular user role"
            )
            
            db.add(admin_role)
            db.add(user_role)
            
            # Create default admin user
            admin_user = User(
                id="user_admin",
                name="admin",
                email="<EMAIL>",
                password=get_password_hash("admin123"),
                is_active=True,
                is_superuser=True
            )
            
            db.add(admin_user)
            
            # Create default system settings
            default_settings = [
                SystemSetting(
                    id="setting_site_name",
                    key="site_name",
                    value="OpenDeepWiki",
                    group="general",
                    value_type="string",
                    description="网站名称",
                    order=1
                ),
                SystemSetting(
                    id="setting_site_description",
                    key="site_description", 
                    value="AI驱动的代码知识库系统",
                    group="general",
                    value_type="string",
                    description="网站描述",
                    order=2
                ),
                SystemSetting(
                    id="setting_max_warehouses",
                    key="max_warehouses_per_user",
                    value="10",
                    group="limits",
                    value_type="integer",
                    description="每个用户最大仓库数量",
                    order=3
                )
            ]
            
            for setting in default_settings:
                db.add(setting)
            
            await db.commit()
            logger.info("Default data created successfully")
            
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(init_default_data())
