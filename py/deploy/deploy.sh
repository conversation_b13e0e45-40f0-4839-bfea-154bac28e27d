#!/bin/bash

# OpenDeepWiki Production Deployment Script

set -e

echo "🚀 Starting OpenDeepWiki deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
DOMAIN=${DOMAIN:-"localhost"}
ENV_FILE=${ENV_FILE:-".env.production"}
COMPOSE_FILE=${COMPOSE_FILE:-"docker-compose.prod.yml"}

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check environment file
    if [ ! -f "$ENV_FILE" ]; then
        log_error "Environment file $ENV_FILE not found"
        log_info "Please copy and configure .env.production from the template"
        exit 1
    fi
    
    log_info "Requirements check passed ✓"
}

setup_directories() {
    log_info "Setting up directories..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p uploads/repositories
    mkdir -p static
    mkdir -p ssl  # For SSL certificates
    
    # Set permissions
    chmod 755 logs uploads static
    
    log_info "Directories setup completed ✓"
}

backup_data() {
    if [ "$1" = "backup" ]; then
        log_info "Creating backup..."
        
        BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # Backup database
        if docker-compose -f "$COMPOSE_FILE" ps db | grep -q "Up"; then
            docker-compose -f "$COMPOSE_FILE" exec -T db pg_dump -U opendeepwiki_user opendeepwiki > "$BACKUP_DIR/database.sql"
            log_info "Database backup created ✓"
        fi
        
        # Backup uploads
        if [ -d "uploads" ]; then
            cp -r uploads "$BACKUP_DIR/"
            log_info "Uploads backup created ✓"
        fi
        
        log_info "Backup completed: $BACKUP_DIR"
    fi
}

deploy_application() {
    log_info "Deploying application..."
    
    # Load environment variables
    export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    
    # Build and start services
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check if services are running
    if docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        log_info "Services are running ✓"
    else
        log_error "Some services failed to start"
        docker-compose -f "$COMPOSE_FILE" logs
        exit 1
    fi
}

run_migrations() {
    log_info "Running database migrations..."
    
    # Wait for database to be ready
    docker-compose -f "$COMPOSE_FILE" exec app python scripts/migrate.py
    
    # Initialize default data
    docker-compose -f "$COMPOSE_FILE" exec app python scripts/init_db.py
    
    log_info "Database setup completed ✓"
}

setup_ssl() {
    if [ "$SETUP_SSL" = "true" ]; then
        log_info "Setting up SSL with Let's Encrypt..."
        
        # Install certbot if not present
        if ! command -v certbot &> /dev/null; then
            log_warning "Certbot not found. Please install certbot for SSL setup"
            return
        fi
        
        # Generate SSL certificate
        certbot certonly --standalone -d "$DOMAIN" --non-interactive --agree-tos --email admin@"$DOMAIN"
        
        # Copy certificates
        cp /etc/letsencrypt/live/"$DOMAIN"/fullchain.pem ssl/
        cp /etc/letsencrypt/live/"$DOMAIN"/privkey.pem ssl/
        
        log_info "SSL setup completed ✓"
    fi
}

setup_monitoring() {
    if [ "$ENABLE_MONITORING" = "true" ]; then
        log_info "Setting up monitoring..."
        
        # Start monitoring services
        docker-compose -f "$COMPOSE_FILE" --profile monitoring up -d
        
        log_info "Monitoring setup completed ✓"
        log_info "Grafana: http://$DOMAIN:3000 (admin/admin)"
        log_info "Prometheus: http://$DOMAIN:9090"
    fi
}

health_check() {
    log_info "Performing health check..."
    
    # Check API health
    if curl -f http://localhost/health &> /dev/null; then
        log_info "API health check passed ✓"
    else
        log_error "API health check failed"
        exit 1
    fi
    
    # Check database connection
    if docker-compose -f "$COMPOSE_FILE" exec db pg_isready -U opendeepwiki_user &> /dev/null; then
        log_info "Database health check passed ✓"
    else
        log_error "Database health check failed"
        exit 1
    fi
}

show_status() {
    log_info "Deployment status:"
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo ""
    log_info "🎉 OpenDeepWiki deployed successfully!"
    log_info "Application URL: http://$DOMAIN"
    log_info "API Documentation: http://$DOMAIN/api/docs"
    log_info "Admin Login: <EMAIL> / admin123"
    echo ""
    log_warning "Remember to:"
    log_warning "1. Change default admin password"
    log_warning "2. Configure your domain and SSL"
    log_warning "3. Set up proper backup strategy"
    log_warning "4. Configure monitoring alerts"
}

# Main deployment flow
main() {
    echo "🐨 OpenDeepWiki Production Deployment"
    echo "======================================"
    
    # Parse arguments
    ACTION=${1:-"deploy"}
    
    case $ACTION in
        "deploy")
            check_requirements
            setup_directories
            backup_data backup
            deploy_application
            run_migrations
            setup_ssl
            setup_monitoring
            health_check
            show_status
            ;;
        "update")
            log_info "Updating application..."
            backup_data backup
            deploy_application
            run_migrations
            health_check
            log_info "Update completed ✓"
            ;;
        "backup")
            backup_data backup
            ;;
        "logs")
            docker-compose -f "$COMPOSE_FILE" logs -f
            ;;
        "stop")
            docker-compose -f "$COMPOSE_FILE" down
            log_info "Application stopped"
            ;;
        "restart")
            docker-compose -f "$COMPOSE_FILE" restart
            log_info "Application restarted"
            ;;
        *)
            echo "Usage: $0 {deploy|update|backup|logs|stop|restart}"
            echo ""
            echo "Commands:"
            echo "  deploy  - Full deployment (default)"
            echo "  update  - Update existing deployment"
            echo "  backup  - Create backup"
            echo "  logs    - Show application logs"
            echo "  stop    - Stop all services"
            echo "  restart - Restart all services"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
