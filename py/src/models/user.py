"""
User and authentication models
"""

from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.orm import relationship

from models.base import BaseEntity


class User(BaseEntity):
    """User model"""
    __tablename__ = "users"
    
    name = Column(String(255), nullable=False, unique=True, index=True, comment="用户名")
    email = Column(String(255), nullable=False, unique=True, index=True, comment="邮箱")
    password = Column(String(255), nullable=False, comment="密码")
    last_login_at = Column(DateTime, nullable=True, index=True, comment="最后登录时间")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    is_superuser = Column(Boolean, default=False, nullable=False, comment="是否超级用户")
    avatar_url = Column(String(500), nullable=True, comment="头像URL")
    
    # Relationships - using string references to avoid circular imports
    user_roles = relationship("UserInRole", back_populates="user", cascade="all, delete-orphan")
    user_auths = relationship("UserInAuth", back_populates="user", cascade="all, delete-orphan")


class Role(BaseEntity):
    """Role model"""
    __tablename__ = "roles"
    
    name = Column(String(100), nullable=False, unique=True, index=True, comment="角色名称")
    description = Column(Text, nullable=False, default="", comment="角色描述")
    
    # Relationships
    user_roles = relationship("UserInRole", back_populates="role", cascade="all, delete-orphan")
    role_permissions = relationship("RolePermission", back_populates="role", cascade="all, delete-orphan")
    warehouse_permissions = relationship("WarehousePermission", back_populates="role", cascade="all, delete-orphan")


class UserInRole(BaseEntity):
    """User role association model"""
    __tablename__ = "user_in_roles"
    
    user_id = Column(String, ForeignKey("users.id"), primary_key=True, comment="用户Id")
    role_id = Column(String, ForeignKey("roles.id"), primary_key=True, comment="角色Id")
    
    # Remove id column since we're using composite primary key
    id = None
    
    # Relationships
    user = relationship("User", back_populates="user_roles")
    role = relationship("Role", back_populates="user_roles")


class UserInAuth(BaseEntity):
    """User authentication provider model"""
    __tablename__ = "user_in_auths"
    
    user_id = Column(String, ForeignKey("users.id"), nullable=False, index=True, comment="用户Id")
    provider = Column(String(50), nullable=False, index=True, comment="认证提供方")
    auth_id = Column(String(255), nullable=False, index=True, comment="认证Id")
    
    # Relationships
    user = relationship("User", back_populates="user_auths")
