"""
Statistics related models
"""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, Integer, BigInteger
from models.base import BaseEntity


class DailyStatistics(BaseEntity):
    """Daily statistics model"""
    __tablename__ = "daily_statistics"
    
    date = Column(DateTime, nullable=False, index=True, comment="统计日期")
    new_users_count = Column(Integer, default=0, nullable=False, comment="新增用户数")
    new_repositories_count = Column(Integer, default=0, nullable=False, comment="新增仓库数")
    new_documents_count = Column(Integer, default=0, nullable=False, comment="新增文档数")
    page_views = Column(Integer, default=0, nullable=False, comment="页面访问量")
    unique_visitors = Column(Integer, default=0, nullable=False, comment="独立访问用户数")
    active_users = Column(Integer, default=0, nullable=False, comment="活跃用户数")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")


class AccessRecord(BaseEntity):
    """Access record model"""
    __tablename__ = "access_records"
    
    resource_type = Column(String(50), nullable=False, index=True, comment="访问的资源类型")
    resource_id = Column(String, nullable=False, index=True, comment="访问的资源ID")
    user_id = Column(String, nullable=True, index=True, comment="访问用户ID")
    ip_address = Column(String(45), nullable=False, comment="访问者IP地址")
    user_agent = Column(String(500), nullable=False, comment="用户代理信息")
    path = Column(String(500), nullable=False, comment="访问路径")
    method = Column(String(10), nullable=False, comment="访问方法")
    status_code = Column(Integer, nullable=False, comment="响应状态码")
    response_time = Column(BigInteger, nullable=False, comment="响应时间（毫秒）")
