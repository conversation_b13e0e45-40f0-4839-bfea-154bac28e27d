"""
Document and related models
"""

from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy import Column, String, DateTime, Integer, Text, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship

from models.base import BaseEntity, SoftDeleteMixin


class Document(BaseEntity):
    """Document model"""
    __tablename__ = "documents"
    
    warehouse_id = Column(String, ForeignKey("warehouses.id"), nullable=False, index=True, comment="关联仓库id")
    last_update = Column(DateTime, default=datetime.utcnow, nullable=False, comment="最后更新时间")
    description = Column(Text, nullable=False, default="", comment="文档介绍")
    like_count = Column(Integer, default=0, nullable=False, comment="浏览量")
    comment_count = Column(Integer, default=0, nullable=False, comment="评论量")
    git_path = Column(String(500), nullable=False, default="", comment="本地git仓库地址")
    status = Column(String(50), nullable=False, comment="仓库状态")
    
    # Relationships
    warehouse = relationship("Warehouse", back_populates="documents")
    document_catalogs = relationship("DocumentCatalog", back_populates="document", cascade="all, delete-orphan")
    document_overviews = relationship("DocumentOverview", back_populates="document", cascade="all, delete-orphan")
    commit_records = relationship("DocumentCommitRecord", back_populates="document", cascade="all, delete-orphan")


class DocumentCatalog(BaseEntity, SoftDeleteMixin):
    """Document catalog/directory model"""
    __tablename__ = "document_catalogs"
    
    name = Column(String(255), nullable=False, index=True, comment="目录名称")
    description = Column(Text, nullable=False, default="", comment="目录描述")
    parent_id = Column(String, ForeignKey("document_catalogs.id"), nullable=True, index=True, comment="父级目录Id")
    warehouse_id = Column(String, ForeignKey("warehouses.id"), nullable=False, index=True, comment="所属仓库Id")
    document_id = Column(String, ForeignKey("documents.id"), nullable=True, index=True, comment="文档Id")
    
    # Relationships
    parent = relationship("DocumentCatalog", remote_side="DocumentCatalog.id", back_populates="children")
    children = relationship("DocumentCatalog", back_populates="parent", cascade="all, delete-orphan")
    warehouse = relationship("Warehouse", back_populates="document_catalogs")
    document = relationship("Document", back_populates="document_catalogs")
    document_file_items = relationship("DocumentFileItem", back_populates="document_catalog", cascade="all, delete-orphan")
    i18n_translations = relationship("DocumentCatalogI18n", back_populates="document_catalog", cascade="all, delete-orphan")


class DocumentFileItem(BaseEntity):
    """Document file item model"""
    __tablename__ = "document_file_items"
    
    title = Column(String(255), nullable=False, index=True, comment="文件标题")
    description = Column(Text, nullable=False, default="", comment="文件描述")
    document_catalog_id = Column(String, ForeignKey("document_catalogs.id"), nullable=False, index=True, comment="目录Id")
    meta_data = Column(JSON, nullable=True, comment="元数据")
    extra = Column(JSON, nullable=True, comment="扩展信息")
    
    # Relationships
    document_catalog = relationship("DocumentCatalog", back_populates="document_file_items")
    file_item_sources = relationship("DocumentFileItemSource", back_populates="document_file_item", cascade="all, delete-orphan")
    i18n_translations = relationship("DocumentFileItemI18n", back_populates="document_file_item", cascade="all, delete-orphan")


class DocumentFileItemSource(BaseEntity):
    """Document file item source model"""
    __tablename__ = "document_file_item_sources"
    
    name = Column(String(255), nullable=False, index=True, comment="来源名称")
    document_file_item_id = Column(String, ForeignKey("document_file_items.id"), nullable=False, index=True, comment="文件项Id")
    
    # Relationships
    document_file_item = relationship("DocumentFileItem", back_populates="file_item_sources")


class DocumentCatalogI18n(BaseEntity):
    """Document catalog internationalization model"""
    __tablename__ = "document_catalog_i18ns"
    
    document_catalog_id = Column(String, ForeignKey("document_catalogs.id"), nullable=False, index=True, comment="文档目录Id")
    language_code = Column(String(10), nullable=False, index=True, comment="语言代码")
    name = Column(String(255), nullable=False, comment="多语言目录名称")
    description = Column(Text, nullable=False, default="", comment="多语言目录描述")
    
    # Relationships
    document_catalog = relationship("DocumentCatalog", back_populates="i18n_translations")


class DocumentFileItemI18n(BaseEntity):
    """Document file item internationalization model"""
    __tablename__ = "document_file_item_i18ns"
    
    document_file_item_id = Column(String, ForeignKey("document_file_items.id"), nullable=False, index=True, comment="文档文件Id")
    language_code = Column(String(10), nullable=False, index=True, comment="语言代码")
    title = Column(String(255), nullable=False, comment="多语言标题")
    description = Column(Text, nullable=False, default="", comment="多语言描述")
    content = Column(Text, nullable=False, comment="多语言内容")
    
    # Relationships
    document_file_item = relationship("DocumentFileItem", back_populates="i18n_translations")


class DocumentOverview(BaseEntity):
    """Document overview model"""
    __tablename__ = "document_overviews"
    
    title = Column(String(255), nullable=False, index=True, comment="文档标题")
    document_id = Column(String, ForeignKey("documents.id"), nullable=False, index=True, comment="文档Id")
    
    # Relationships
    document = relationship("Document", back_populates="document_overviews")


class DocumentCommitRecord(BaseEntity):
    """Document commit record model"""
    __tablename__ = "document_commit_records"
    
    commit_message = Column(Text, nullable=False, comment="提交信息")
    author = Column(String(255), nullable=False, comment="作者")
    warehouse_id = Column(String, ForeignKey("warehouses.id"), nullable=False, index=True, comment="仓库Id")
    document_id = Column(String, ForeignKey("documents.id"), nullable=True, index=True, comment="文档Id")
    commit_id = Column(String(255), nullable=False, index=True, comment="提交Id")
    
    # Relationships
    document = relationship("Document", back_populates="commit_records")


class TranslationTask(BaseEntity):
    """Translation task model"""
    __tablename__ = "translation_tasks"
    
    name = Column(String(255), nullable=False, comment="任务名称")
    status = Column(String(50), nullable=False, comment="任务状态")
    from_language = Column(String(10), nullable=False, comment="源语言")
    to_language = Column(String(10), nullable=False, comment="目标语言")
    warehouse_id = Column(String, ForeignKey("warehouses.id"), nullable=True, index=True, comment="仓库Id")
    document_catalog_id = Column(String, ForeignKey("document_catalogs.id"), nullable=True, index=True, comment="目录Id")
    progress = Column(Integer, default=0, comment="进度百分比")
    error_message = Column(Text, nullable=True, comment="错误信息")
