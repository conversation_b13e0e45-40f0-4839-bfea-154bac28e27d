"""
Base models and mixins for OpenDeepWiki
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import Column, String, DateTime, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from core.database import Base


class BaseEntity(Base):
    """Base entity with common fields"""
    __abstract__ = True
    
    id = Column(String, primary_key=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class CreateEntityMixin:
    """Mixin for entities that track creation time"""
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)


class SoftDeleteMixin:
    """Mixin for soft delete functionality"""
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime, nullable=True)
    
    def soft_delete(self):
        """Mark entity as deleted"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """Restore deleted entity"""
        self.is_deleted = False
        self.deleted_at = None
