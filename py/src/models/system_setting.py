"""
System setting model
"""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, Boolean, Integer, Text
from models.base import BaseEntity


class SystemSetting(BaseEntity):
    """System setting model"""
    __tablename__ = "system_settings"
    
    key = Column(String(100), nullable=False, unique=True, index=True, comment="设置键名")
    value = Column(Text, nullable=True, comment="设置值")
    group = Column(String(50), nullable=False, index=True, comment="设置分组")
    value_type = Column(String(20), nullable=False, default="string", comment="设置类型")
    description = Column(String(500), nullable=True, comment="设置描述")
    is_sensitive = Column(Boolean, default=False, nullable=False, comment="是否敏感信息")
    requires_restart = Column(Boolean, default=False, nullable=False, comment="是否需要重启生效")
    default_value = Column(Text, nullable=True, comment="默认值")
    order = Column(Integer, default=0, nullable=False, comment="排序顺序")
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")
