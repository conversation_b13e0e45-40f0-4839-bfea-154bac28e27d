"""
Warehouse and related models
"""

from datetime import datetime
from enum import Enum
from typing import Optional, List

from sqlalchemy import Column, String, DateTime, Integer, Text, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship

from models.base import BaseEntity, SoftDeleteMixin


class WarehouseType(str, Enum):
    """Warehouse type enumeration"""
    GITHUB = "github"
    GITLAB = "gitlab"
    GITEE = "gitee"
    LOCAL = "local"


class WarehouseStatus(str, Enum):
    """Warehouse status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    UPDATING = "updating"


class SyncTrigger(str, Enum):
    """Sync trigger enumeration"""
    MANUAL = "manual"
    SCHEDULED = "scheduled"
    WEBHOOK = "webhook"


class SyncStatus(str, Enum):
    """Sync status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Warehouse(BaseEntity):
    """Knowledge warehouse/repository model"""
    __tablename__ = "warehouses"
    
    name = Column(String(255), nullable=False, index=True, comment="仓库名称")
    description = Column(Text, nullable=False, default="", comment="仓库描述")
    address = Column(String(500), nullable=True, index=True, comment="仓库地址")
    type = Column(String(50), nullable=False, default=WarehouseType.GITHUB, index=True, comment="仓库类型")
    branch = Column(String(100), nullable=True, default="main", index=True, comment="分支")
    organization_name = Column(String(255), nullable=True, index=True, comment="组织名称")
    status = Column(String(50), nullable=False, default=WarehouseStatus.PENDING, index=True, comment="仓库状态")
    
    # Relationships
    documents = relationship("Document", back_populates="warehouse", cascade="all, delete-orphan")
    document_catalogs = relationship("DocumentCatalog", back_populates="warehouse", cascade="all, delete-orphan")
    warehouse_permissions = relationship("WarehousePermission", back_populates="warehouse", cascade="all, delete-orphan")
    sync_records = relationship("WarehouseSyncRecord", back_populates="warehouse", cascade="all, delete-orphan")


class WarehouseSyncRecord(BaseEntity):
    """Warehouse synchronization record model"""
    __tablename__ = "warehouse_sync_records"
    
    warehouse_id = Column(String, ForeignKey("warehouses.id"), nullable=False, index=True, comment="仓库Id")
    status = Column(String(50), nullable=False, index=True, comment="同步状态")
    start_time = Column(DateTime, nullable=False, index=True, comment="同步开始时间")
    end_time = Column(DateTime, nullable=True, comment="同步结束时间")
    from_version = Column(String(255), nullable=True, comment="同步前的版本")
    to_version = Column(String(255), nullable=True, comment="同步后的版本")
    error_message = Column(Text, nullable=True, comment="错误信息")
    file_count = Column(Integer, nullable=True, comment="同步的文件数量")
    updated_file_count = Column(Integer, nullable=True, comment="更新的文件数量")
    added_file_count = Column(Integer, nullable=True, comment="新增的文件数量")
    deleted_file_count = Column(Integer, nullable=True, comment="删除的文件数量")
    trigger = Column(String(50), nullable=False, index=True, comment="同步触发方式")
    
    # Relationships
    warehouse = relationship("Warehouse", back_populates="sync_records")


class WarehouseInRole(BaseEntity):
    """Warehouse role association model"""
    __tablename__ = "warehouse_in_roles"
    
    warehouse_id = Column(String, ForeignKey("warehouses.id"), primary_key=True, comment="仓库Id")
    role_id = Column(String, ForeignKey("roles.id"), primary_key=True, comment="角色Id")
    
    # Remove id column since we're using composite primary key
    id = None
