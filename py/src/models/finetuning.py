"""
Fine-tuning related models
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, String, DateTime, Text, ForeignKey
from models.base import BaseEntity


class FineTuningTaskStatus(str, Enum):
    """Fine-tuning task status"""
    NOT_STARTED = "NotStarted"
    IN_PROGRESS = "InProgress"
    COMPLETED = "Completed"
    FAILED = "Failed"
    CANCELLED = "Cancelled"


class TrainingDatasetStatus(str, Enum):
    """Training dataset status"""
    NOT_STARTED = "NotStarted"
    IN_PROGRESS = "InProgress"
    COMPLETED = "Completed"
    FAILED = "Failed"


class FineTuningTask(BaseEntity):
    """Fine-tuning task model"""
    __tablename__ = "fine_tuning_tasks"
    
    warehouse_id = Column(String, nullable=False, index=True, comment="仓库ID")
    training_dataset_id = Column(String, nullable=False, index=True, comment="训练数据集ID")
    document_catalog_id = Column(String, nullable=False, index=True, comment="数据集目录ID")
    name = Column(String(255), nullable=False, comment="任务名称")
    user_id = Column(String, nullable=False, index=True, comment="用户ID")
    description = Column(Text, nullable=False, default="", comment="任务描述")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="任务创建时间")
    started_at = Column(DateTime, nullable=True, comment="任务开始时间")
    completed_at = Column(DateTime, nullable=True, comment="任务完成时间")
    status = Column(String(50), nullable=False, default=FineTuningTaskStatus.NOT_STARTED, comment="任务状态")
    dataset = Column(Text, nullable=False, default="", comment="数据集")
    error = Column(Text, nullable=True, comment="错误信息")
    original_dataset = Column(Text, nullable=True, comment="原始数据集")


class TrainingDataset(BaseEntity):
    """Training dataset model"""
    __tablename__ = "training_datasets"
    
    warehouse_id = Column(String, nullable=False, index=True, comment="仓库ID")
    user_id = Column(String, nullable=False, index=True, comment="用户ID")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="数据集生成时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="数据集最后更新时间")
    status = Column(String(50), nullable=False, default=TrainingDatasetStatus.NOT_STARTED, comment="数据集状态")
    name = Column(String(255), nullable=False, comment="数据集名称")
    endpoint = Column(String(500), nullable=False, default="", comment="API接口地址")
    api_key = Column(String(500), nullable=False, default="", comment="API密钥")
    prompt = Column(Text, nullable=False, default="", comment="Prompt")
    model = Column(String(255), nullable=False, default="", comment="模型名称")
