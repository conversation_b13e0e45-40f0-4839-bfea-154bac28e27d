"""
System and configuration models
"""

from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy import Column, String, DateTime, Text, JSON, Boolean, Integer, ForeignKey
from sqlalchemy.orm import relationship

from models.base import BaseEntity


class AppConfig(BaseEntity):
    """Application configuration model"""
    __tablename__ = "app_configs"
    
    app_id = Column(String(255), nullable=False, unique=True, index=True, comment="应用ID")
    name = Column(String(255), nullable=False, index=True, comment="应用名称")
    organization_name = Column(String(255), nullable=False, index=True, comment="组织名称")
    repository_name = Column(String(255), nullable=False, index=True, comment="仓库名称")
    allowed_domains_json = Column(Text, nullable=False, comment="允许的域名列表JSON")
    enable_domain_validation = Column(Boolean, default=True, comment="是否启用域名验证")
    description = Column(Text, nullable=True, comment="应用描述")
    prompt = Column(Text, nullable=True, comment="默认提示词")
    introduction = Column(Text, nullable=True, comment="开场白")
    model = Column(String(100), nullable=True, comment="选择模型")
    user_id = Column(String, ForeignKey("users.id"), nullable=False, index=True, comment="创建用户ID")
    is_enabled = Column(Boolean, default=True, index=True, comment="是否启用")
    last_used_at = Column(DateTime, nullable=True, comment="最后使用时间")
    mcps = Column(JSON, nullable=True, comment="MCP配置")
    recommended_questions = Column(JSON, nullable=True, comment="推荐问题")
    
    # Relationships
    user = relationship("User", back_populates="app_configs")


class SystemSetting(BaseEntity):
    """System settings model"""
    __tablename__ = "system_settings"
    
    key = Column(String(255), nullable=False, unique=True, index=True, comment="设置键名")
    value = Column(Text, nullable=True, comment="设置值")
    group = Column(String(100), nullable=False, index=True, comment="设置分组")
    value_type = Column(String(50), nullable=False, comment="设置类型")
    description = Column(Text, nullable=True, comment="设置描述")
    is_sensitive = Column(Boolean, default=False, comment="是否敏感信息")
    requires_restart = Column(Boolean, default=False, comment="是否需要重启生效")
    default_value = Column(Text, nullable=True, comment="默认值")
    order = Column(Integer, default=0, index=True, comment="排序顺序")
    is_enabled = Column(Boolean, default=True, index=True, comment="是否启用")


class MiniMap(BaseEntity):
    """Mini map model for warehouse visualization"""
    __tablename__ = "mini_maps"
    
    warehouse_id = Column(String, ForeignKey("warehouses.id"), nullable=False, index=True, comment="仓库Id")
    value = Column(Text, nullable=False, comment="小地图数据")
    
    # Relationships
    warehouse = relationship("Warehouse")
