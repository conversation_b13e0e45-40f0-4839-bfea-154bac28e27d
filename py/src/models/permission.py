"""
Permission model
"""

from datetime import datetime
from sqlalchemy import Column, String, Boolean, ForeignKey, Text, DateTime
from sqlalchemy.orm import relationship
from models.base import BaseEntity


class Permission(BaseEntity):
    """Permission model"""
    __tablename__ = "permissions"
    
    name = Column(String(255), nullable=False, comment="权限名称")
    code = Column(String(100), nullable=False, unique=True, comment="权限代码")
    description = Column(Text, nullable=True, comment="权限描述")
    resource_type = Column(String(50), nullable=False, comment="资源类型")
    action = Column(String(50), nullable=False, comment="操作类型")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")


class RolePermission(BaseEntity):
    """Role permission model"""
    __tablename__ = "role_permissions"
    
    role_id = Column(String, ForeignKey("roles.id"), nullable=False, index=True, comment="角色ID")
    permission_id = Column(String, ForeignKey("permissions.id"), nullable=False, index=True, comment="权限ID")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    
    # Relationships
    role = relationship("Role", back_populates="role_permissions")
    permission = relationship("Permission", back_populates="role_permissions")


class WarehousePermission(BaseEntity):
    """Warehouse permission model"""
    __tablename__ = "warehouse_permissions"
    
    role_id = Column(String, ForeignKey("roles.id"), nullable=False, index=True, comment="角色ID")
    warehouse_id = Column(String, ForeignKey("warehouses.id"), nullable=False, index=True, comment="仓库ID")
    is_read_only = Column(Boolean, default=True, nullable=False, comment="是否只读权限")
    is_write = Column(Boolean, default=False, nullable=False, comment="是否有写入权限")
    is_delete = Column(Boolean, default=False, nullable=False, comment="是否有删除权限")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")
    
    # Relationships
    role = relationship("Role", back_populates="warehouse_permissions")
    warehouse = relationship("Warehouse", back_populates="warehouse_permissions")
