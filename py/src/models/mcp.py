"""
MCP (Model Context Protocol) models
"""

from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy import Column, String, DateTime, Text, JSON, ForeignKey
from sqlalchemy.orm import relationship

from models.base import BaseEntity


class MCPHistory(BaseEntity):
    """MCP interaction history model"""
    __tablename__ = "mcp_histories"
    
    user_id = Column(String, ForeignKey("users.id"), nullable=False, index=True, comment="用户Id")
    warehouse_id = Column(String, ForeignKey("warehouses.id"), nullable=True, index=True, comment="仓库Id")
    message = Column(Text, nullable=False, comment="用户消息")
    response = Column(Text, nullable=False, comment="AI响应")
    context = Column(JSON, nullable=True, comment="上下文信息")
    
    # Relationships
    user = relationship("User")


class AgentTodo(BaseEntity):
    """Agent todo model"""
    __tablename__ = "agent_todos"
    
    user_id = Column(String, ForeignKey("users.id"), nullable=False, index=True, comment="用户Id")
    warehouse_id = Column(String, ForeignKey("warehouses.id"), nullable=True, index=True, comment="仓库Id")
    title = Column(String(255), nullable=False, comment="任务标题")
    description = Column(Text, nullable=False, default="", comment="任务描述")
    status = Column(String(50), nullable=False, default="pending", comment="任务状态")
    priority = Column(String(50), nullable=False, default="medium", comment="优先级")
    
    # Relationships
    user = relationship("User")
