"""
Menu model
"""

from datetime import datetime
from sqlalchemy import Column, String, Integer, Boolean, Text, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from models.base import BaseEntity


class Menu(BaseEntity):
    """Menu model"""
    __tablename__ = "menus"
    
    name = Column(String(255), nullable=False, comment="菜单名称")
    path = Column(String(500), nullable=False, comment="菜单路径")
    icon = Column(String(100), nullable=True, comment="菜单图标")
    order = Column(Integer, default=0, nullable=False, comment="排序")
    is_hidden = Column(Boolean, default=False, nullable=False, comment="是否隐藏")
    parent_id = Column(String, ForeignKey("menus.id"), nullable=True, index=True, comment="父菜单ID")
    required_roles = Column(Text, nullable=True, comment="所需角色（JSON格式）")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")
    
    # Relationships
    parent = relationship("Menu", remote_side="Menu.id", back_populates="children")
    children = relationship("Menu", back_populates="parent", cascade="all, delete-orphan")
