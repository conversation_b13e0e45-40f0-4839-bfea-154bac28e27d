"""
Menu API endpoints
"""

from typing import List, Optional
from datetime import datetime
import json

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user, require_admin
from models.user import User
from models.menu import Menu
from schemas.menu import (
    MenuItemResponse,
    UserMenuResponse,
    MenuCreateRequest,
    MenuUpdateRequest
)

router = APIRouter()


@router.get("/user-menu", response_model=UserMenuResponse)
async def get_user_menu(
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """获取当前用户的菜单"""
    
    # 获取所有菜单
    menu_query = select(Menu).where(Menu.is_hidden == False).order_by(Menu.order)
    menu_result = await db.execute(menu_query)
    all_menus = menu_result.scalars().all()
    
    # 根据用户角色过滤菜单
    user_roles = [role.name for role in current_user.roles] if hasattr(current_user, 'roles') else []
    filtered_menus = []
    
    for menu in all_menus:
        # 检查菜单权限
        if menu.required_roles:
            try:
                required_roles = json.loads(menu.required_roles)
                if not any(role in user_roles for role in required_roles):
                    continue
            except (json.JSONDecodeError, TypeError):
                continue
        
        filtered_menus.append(menu)
    
    # 构建菜单树
    menu_dict = {menu.id: menu for menu in filtered_menus}
    root_menus = []
    
    for menu in filtered_menus:
        if menu.parent_id is None:
            root_menus.append(menu)
        else:
            parent = menu_dict.get(menu.parent_id)
            if parent:
                if not hasattr(parent, 'children'):
                    parent.children = []
                parent.children.append(menu)
    
    # 转换为响应格式
    menu_items = []
    for menu in root_menus:
        menu_items.append(_convert_menu_to_response(menu))
    
    return UserMenuResponse(
        user={
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email,
            "roles": user_roles
        },
        menus=menu_items
    )


@router.get("/", response_model=List[MenuItemResponse])
async def list_menus(
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取所有菜单（管理员）"""
    
    menu_query = select(Menu).order_by(Menu.order)
    menu_result = await db.execute(menu_query)
    menus = menu_result.scalars().all()
    
    # 构建菜单树
    menu_dict = {menu.id: menu for menu in menus}
    root_menus = []
    
    for menu in menus:
        if menu.parent_id is None:
            root_menus.append(menu)
        else:
            parent = menu_dict.get(menu.parent_id)
            if parent:
                if not hasattr(parent, 'children'):
                    parent.children = []
                parent.children.append(menu)
    
    return [_convert_menu_to_response(menu) for menu in root_menus]


@router.post("/", response_model=MenuItemResponse)
async def create_menu(
    request: MenuCreateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """创建菜单（管理员）"""
    
    menu = Menu(
        name=request.name,
        path=request.path,
        icon=request.icon,
        order=request.order,
        is_hidden=request.is_hidden,
        parent_id=request.parent_id,
        required_roles=json.dumps(request.required_roles) if request.required_roles else None
    )
    
    db.add(menu)
    await db.commit()
    await db.refresh(menu)
    
    return _convert_menu_to_response(menu)


@router.get("/{menu_id}", response_model=MenuItemResponse)
async def get_menu(
    menu_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取菜单详情（管理员）"""
    
    menu_query = select(Menu).where(Menu.id == menu_id)
    menu_result = await db.execute(menu_query)
    menu = menu_result.scalar_one_or_none()
    
    if not menu:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu not found"
        )
    
    return _convert_menu_to_response(menu)


@router.put("/{menu_id}", response_model=MenuItemResponse)
async def update_menu(
    menu_id: str,
    request: MenuUpdateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """更新菜单（管理员）"""
    
    menu_query = select(Menu).where(Menu.id == menu_id)
    menu_result = await db.execute(menu_query)
    menu = menu_result.scalar_one_or_none()
    
    if not menu:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu not found"
        )
    
    # 更新字段
    if request.name is not None:
        menu.name = request.name
    if request.path is not None:
        menu.path = request.path
    if request.icon is not None:
        menu.icon = request.icon
    if request.order is not None:
        menu.order = request.order
    if request.is_hidden is not None:
        menu.is_hidden = request.is_hidden
    if request.parent_id is not None:
        menu.parent_id = request.parent_id
    if request.required_roles is not None:
        menu.required_roles = json.dumps(request.required_roles) if request.required_roles else None
    
    menu.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(menu)
    
    return _convert_menu_to_response(menu)


@router.delete("/{menu_id}")
async def delete_menu(
    menu_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """删除菜单（管理员）"""
    
    menu_query = select(Menu).where(Menu.id == menu_id)
    menu_result = await db.execute(menu_query)
    menu = menu_result.scalar_one_or_none()
    
    if not menu:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu not found"
        )
    
    await db.delete(menu)
    await db.commit()
    
    return {"message": "Menu deleted successfully"}


def _convert_menu_to_response(menu: Menu) -> MenuItemResponse:
    """将Menu模型转换为MenuItemResponse"""
    
    # 解析required_roles
    required_roles = []
    if menu.required_roles:
        try:
            required_roles = json.loads(menu.required_roles)
        except (json.JSONDecodeError, TypeError):
            required_roles = []
    
    # 转换子菜单
    children = []
    if hasattr(menu, 'children') and menu.children:
        children = [_convert_menu_to_response(child) for child in menu.children]
    
    return MenuItemResponse(
        id=menu.id,
        name=menu.name,
        path=menu.path,
        icon=menu.icon,
        order=menu.order,
        is_hidden=menu.is_hidden,
        required_roles=required_roles,
        children=children
    )
