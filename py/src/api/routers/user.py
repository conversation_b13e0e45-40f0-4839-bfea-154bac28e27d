"""
User management API endpoints
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user, require_admin
from models.user import User, Role, UserInRole
from schemas.user import (
    UserResponse,
    UserListResponse,
    UserUpdateRequest,
    RoleResponse,
    UserRoleAssignRequest
)

router = APIRouter()


@router.get("/", response_model=UserListResponse)
async def list_users(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """List users (admin only)"""
    
    query = select(User).options(selectinload(User.user_roles))
    
    # Add filters
    conditions = []
    
    if search:
        conditions.append(
            or_(
                User.name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%")
            )
        )
    
    if is_active is not None:
        conditions.append(User.is_active == is_active)
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # Add pagination
    offset = (page - 1) * size
    query = query.offset(offset).limit(size)
    
    # Execute query
    result = await db.execute(query)
    users = result.scalars().all()
    
    # Get total count
    count_query = select(User)
    if conditions:
        count_query = count_query.where(and_(*conditions))
    
    count_result = await db.execute(count_query)
    total = len(count_result.scalars().all())
    
    return UserListResponse(
        items=[UserResponse.from_orm(user) for user in users],
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get user by ID"""
    
    # Users can only view their own profile unless they are admin
    if user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view this user"
        )
    
    result = await db.execute(
        select(User)
        .options(selectinload(User.user_roles))
        .where(User.id == user_id)
    )
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return UserResponse.from_orm(user)


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    user_request: UserUpdateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Update user"""
    
    # Users can only update their own profile unless they are admin
    if user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this user"
        )
    
    result = await db.execute(
        select(User).where(User.id == user_id)
    )
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update fields
    update_data = user_request.dict(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(user, field):
            setattr(user, field, value)
    
    user.updated_at = datetime.utcnow()
    await db.commit()
    await db.refresh(user)
    
    return UserResponse.from_orm(user)


@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """Delete user (admin only)"""
    
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )
    
    result = await db.execute(
        select(User).where(User.id == user_id)
    )
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    await db.delete(user)
    await db.commit()
    
    return {"message": "User deleted successfully"}


@router.get("/roles/", response_model=List[RoleResponse])
async def list_roles(
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """List all roles (admin only)"""
    
    result = await db.execute(select(Role))
    roles = result.scalars().all()
    
    return [RoleResponse.from_orm(role) for role in roles]


@router.post("/{user_id}/roles")
async def assign_user_role(
    user_id: str,
    role_request: UserRoleAssignRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """Assign role to user (admin only)"""
    
    # Verify user exists
    result = await db.execute(
        select(User).where(User.id == user_id)
    )
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Verify role exists
    result = await db.execute(
        select(Role).where(Role.id == role_request.role_id)
    )
    role = result.scalar_one_or_none()
    
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # Check if assignment already exists
    result = await db.execute(
        select(UserInRole).where(
            and_(
                UserInRole.user_id == user_id,
                UserInRole.role_id == role_request.role_id
            )
        )
    )
    existing = result.scalar_one_or_none()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has this role"
        )
    
    # Create role assignment
    user_role = UserInRole(
        user_id=user_id,
        role_id=role_request.role_id
    )
    
    db.add(user_role)
    await db.commit()
    
    return {"message": "Role assigned successfully"}


@router.delete("/{user_id}/roles/{role_id}")
async def remove_user_role(
    user_id: str,
    role_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """Remove role from user (admin only)"""
    
    result = await db.execute(
        select(UserInRole).where(
            and_(
                UserInRole.user_id == user_id,
                UserInRole.role_id == role_id
            )
        )
    )
    user_role = result.scalar_one_or_none()
    
    if not user_role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User role assignment not found"
        )
    
    await db.delete(user_role)
    await db.commit()
    
    return {"message": "Role removed successfully"}
