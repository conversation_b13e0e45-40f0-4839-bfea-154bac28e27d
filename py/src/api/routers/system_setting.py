"""
System setting API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user, require_admin
from models.user import User
from models.system_setting import SystemSetting
from schemas.system_setting import (
    SystemSettingResponse,
    SystemSettingCreateRequest,
    SystemSettingUpdateRequest,
    SystemSettingListResponse
)

router = APIRouter()


@router.get("/", response_model=SystemSettingListResponse)
async def list_settings(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    group: Optional[str] = Query(None, description="设置分组"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_enabled: Optional[bool] = Query(None, description="是否启用"),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取系统设置列表（管理员）"""
    
    query = select(SystemSetting)
    
    if group:
        query = query.where(SystemSetting.group == group)
    
    if search:
        query = query.where(
            or_(
                SystemSetting.key.contains(search),
                SystemSetting.description.contains(search)
            )
        )
    
    if is_enabled is not None:
        query = query.where(SystemSetting.is_enabled == is_enabled)
    
    # 获取总数
    count_query = select(func.count(SystemSetting.id))
    if group:
        count_query = count_query.where(SystemSetting.group == group)
    if search:
        count_query = count_query.where(
            or_(
                SystemSetting.key.contains(search),
                SystemSetting.description.contains(search)
            )
        )
    if is_enabled is not None:
        count_query = count_query.where(SystemSetting.is_enabled == is_enabled)
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 获取分页结果
    query = query.offset((page - 1) * size).limit(size).order_by(SystemSetting.group, SystemSetting.order)
    result = await db.execute(query)
    settings = result.scalars().all()
    
    return SystemSettingListResponse(
        items=settings,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.get("/groups")
async def get_setting_groups(
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取设置分组列表（管理员）"""
    
    query = select(SystemSetting.group).distinct().order_by(SystemSetting.group)
    result = await db.execute(query)
    groups = result.scalars().all()
    
    return {"groups": groups}


@router.get("/{setting_key}", response_model=SystemSettingResponse)
async def get_setting(
    setting_key: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取系统设置详情（管理员）"""
    
    query = select(SystemSetting).where(SystemSetting.key == setting_key)
    result = await db.execute(query)
    setting = result.scalar_one_or_none()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Setting not found"
        )
    
    return setting


@router.post("/", response_model=SystemSettingResponse)
async def create_setting(
    request: SystemSettingCreateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """创建系统设置（管理员）"""
    
    # 检查设置键是否已存在
    existing_query = select(SystemSetting).where(SystemSetting.key == request.key)
    existing_result = await db.execute(existing_query)
    existing = existing_result.scalar_one_or_none()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Setting key already exists"
        )
    
    setting = SystemSetting(
        key=request.key,
        value=request.value,
        group=request.group,
        value_type=request.value_type,
        description=request.description,
        is_sensitive=request.is_sensitive,
        requires_restart=request.requires_restart,
        default_value=request.default_value,
        order=request.order,
        is_enabled=request.is_enabled
    )
    
    db.add(setting)
    await db.commit()
    await db.refresh(setting)
    
    return setting


@router.put("/{setting_key}", response_model=SystemSettingResponse)
async def update_setting(
    setting_key: str,
    request: SystemSettingUpdateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """更新系统设置（管理员）"""
    
    query = select(SystemSetting).where(SystemSetting.key == setting_key)
    result = await db.execute(query)
    setting = result.scalar_one_or_none()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Setting not found"
        )
    
    # 更新字段
    if request.value is not None:
        setting.value = request.value
    if request.group is not None:
        setting.group = request.group
    if request.value_type is not None:
        setting.value_type = request.value_type
    if request.description is not None:
        setting.description = request.description
    if request.is_sensitive is not None:
        setting.is_sensitive = request.is_sensitive
    if request.requires_restart is not None:
        setting.requires_restart = request.requires_restart
    if request.default_value is not None:
        setting.default_value = request.default_value
    if request.order is not None:
        setting.order = request.order
    if request.is_enabled is not None:
        setting.is_enabled = request.is_enabled
    
    setting.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(setting)
    
    return setting


@router.delete("/{setting_key}")
async def delete_setting(
    setting_key: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """删除系统设置（管理员）"""
    
    query = select(SystemSetting).where(SystemSetting.key == setting_key)
    result = await db.execute(query)
    setting = result.scalar_one_or_none()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Setting not found"
        )
    
    await db.delete(setting)
    await db.commit()
    
    return {"message": "Setting deleted successfully"}


@router.get("/public/config")
async def get_public_config(
    db: AsyncSession = Depends(get_database_session)
):
    """获取公开配置（无需认证）"""
    
    query = select(SystemSetting).where(
        and_(
            SystemSetting.is_enabled == True,
            SystemSetting.is_sensitive == False
        )
    ).order_by(SystemSetting.group, SystemSetting.order)
    
    result = await db.execute(query)
    settings = result.scalars().all()
    
    config = {}
    for setting in settings:
        if setting.group not in config:
            config[setting.group] = {}
        
        # 根据类型转换值
        value = setting.value
        if setting.value_type == "bool":
            value = value.lower() in ("true", "1", "yes", "on")
        elif setting.value_type == "int":
            try:
                value = int(value) if value else 0
            except ValueError:
                value = 0
        elif setting.value_type == "json":
            try:
                value = json.loads(value) if value else {}
            except (json.JSONDecodeError, TypeError):
                value = {}
        
        config[setting.group][setting.key] = value
    
    return config


@router.post("/batch-update")
async def batch_update_settings(
    settings: Dict[str, Any],
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """批量更新系统设置（管理员）"""
    
    updated_count = 0
    
    for key, value in settings.items():
        query = select(SystemSetting).where(SystemSetting.key == key)
        result = await db.execute(query)
        setting = result.scalar_one_or_none()
        
        if setting:
            # 根据类型转换值
            if setting.value_type == "bool":
                setting.value = str(value).lower()
            elif setting.value_type == "int":
                setting.value = str(int(value))
            elif setting.value_type == "json":
                setting.value = json.dumps(value) if isinstance(value, (dict, list)) else str(value)
            else:
                setting.value = str(value)
            
            setting.updated_at = datetime.utcnow()
            updated_count += 1
    
    await db.commit()
    
    return {
        "message": f"Updated {updated_count} settings successfully",
        "updated_count": updated_count
    }
