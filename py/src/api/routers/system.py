"""
System management API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func

from core.database import get_database_session
from core.auth import get_current_user, require_admin
from models.user import User
from models.warehouse import Warehouse
from models.document import Document
from schemas.system import SystemStatsResponse, SystemHealthResponse

router = APIRouter()


@router.get("/health", response_model=SystemHealthResponse)
async def health_check(
    db: AsyncSession = Depends(get_database_session)
):
    """System health check"""
    
    try:
        # Test database connection
        await db.execute(select(1))
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"
    
    return SystemHealthResponse(
        status="healthy" if db_status == "healthy" else "unhealthy",
        timestamp=datetime.utcnow(),
        database=db_status,
        version="0.9.0"
    )


@router.get("/stats", response_model=SystemStatsResponse)
async def get_system_stats(
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """Get system statistics (admin only)"""
    
    # Get user count
    user_result = await db.execute(select(func.count(User.id)))
    user_count = user_result.scalar()
    
    # Get warehouse count
    warehouse_result = await db.execute(select(func.count(Warehouse.id)))
    warehouse_count = warehouse_result.scalar()
    
    # Get document count
    document_result = await db.execute(select(func.count(Document.id)))
    document_count = document_result.scalar()
    
    # Get active users (users who logged in within last 30 days)
    thirty_days_ago = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    thirty_days_ago = thirty_days_ago.replace(day=thirty_days_ago.day - 30)
    
    active_users_result = await db.execute(
        select(func.count(User.id)).where(
            and_(
                User.last_login_at >= thirty_days_ago,
                User.is_active == True
            )
        )
    )
    active_users = active_users_result.scalar()
    
    # Get warehouses by status
    warehouse_status_result = await db.execute(
        select(Warehouse.status, func.count(Warehouse.id))
        .group_by(Warehouse.status)
    )
    warehouse_by_status = {status: count for status, count in warehouse_status_result.all()}
    
    # Get warehouses by type
    warehouse_type_result = await db.execute(
        select(Warehouse.type, func.count(Warehouse.id))
        .group_by(Warehouse.type)
    )
    warehouse_by_type = {type_: count for type_, count in warehouse_type_result.all()}
    
    return SystemStatsResponse(
        total_users=user_count,
        active_users=active_users,
        total_warehouses=warehouse_count,
        total_documents=document_count,
        warehouse_by_status=warehouse_by_status,
        warehouse_by_type=warehouse_by_type,
        timestamp=datetime.utcnow()
    )


@router.get("/info")
async def get_system_info():
    """Get system information"""
    
    return {
        "name": "OpenDeepWiki",
        "version": "0.9.0",
        "description": "AI-driven code knowledge base system",
        "python_version": "3.11+",
        "framework": "FastAPI",
        "database": "SQLAlchemy with async support"
    }


@router.get("/version")
async def get_version():
    """Get API version"""
    
    return {
        "version": "0.9.0",
        "api_version": "v1",
        "build_time": "2024-01-01T00:00:00Z"
    }


@router.post("/maintenance")
async def toggle_maintenance_mode(
    enabled: bool,
    current_user: User = Depends(require_admin)
):
    """Toggle maintenance mode (admin only)"""
    
    # This would typically update a system setting
    # For now, we'll just return a message
    
    mode = "enabled" if enabled else "disabled"
    return {
        "message": f"Maintenance mode {mode}",
        "enabled": enabled,
        "timestamp": datetime.utcnow()
    }


@router.get("/logs")
async def get_system_logs(
    level: Optional[str] = Query("INFO"),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(require_admin)
):
    """Get system logs (admin only)"""
    
    # This would typically read from log files or log database
    # For now, we'll return a mock response
    
    logs = [
        {
            "timestamp": datetime.utcnow(),
            "level": "INFO",
            "message": "System startup completed",
            "module": "main"
        },
        {
            "timestamp": datetime.utcnow(),
            "level": "INFO", 
            "message": "Database connection established",
            "module": "database"
        }
    ]
    
    return {
        "logs": logs[:limit],
        "total": len(logs),
        "level": level,
        "limit": limit
    }
