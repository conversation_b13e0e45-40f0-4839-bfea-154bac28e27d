"""
API routers for OpenDeepWiki
"""

from .auth import router as auth_router
from .warehouse import router as warehouse_router
from .document import router as document_router
from .user import router as user_router
from .system import router as system_router
from .mcp import router as mcp_router
from .role import router as role_router
from .finetuning import router as finetuning_router
from .menu import router as menu_router
from .permission import router as permission_router
from .statistics import router as statistics_router
from .system_setting import router as system_setting_router
from .document_catalog import router as document_catalog_router

__all__ = [
    "auth_router",
    "warehouse_router", 
    "document_router",
    "user_router",
    "system_router",
    "mcp_router",
    "role_router",
    "finetuning_router",
    "menu_router",
    "permission_router",
    "statistics_router",
    "system_setting_router",
    "document_catalog_router"
]
