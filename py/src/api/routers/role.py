"""
Role management API endpoints
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user, require_admin
from models.user import User, Role, UserInRole
from schemas.role import (
    RoleResponse,
    RoleListResponse,
    RoleCreateRequest,
    RoleUpdateRequest,
    RoleInfoResponse
)

router = APIRouter()


@router.get("/", response_model=RoleListResponse)
async def list_roles(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """List roles (admin only)"""
    
    query = select(Role)
    
    # Add filters
    conditions = []
    
    if search:
        conditions.append(
            or_(
                Role.name.ilike(f"%{search}%"),
                Role.description.ilike(f"%{search}%")
            )
        )
    
    if is_active is not None:
        conditions.append(Role.is_active == is_active)
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # Add pagination
    offset = (page - 1) * size
    query = query.offset(offset).limit(size)
    
    # Execute query
    result = await db.execute(query)
    roles = result.scalars().all()
    
    # Get total count
    count_query = select(Role)
    if conditions:
        count_query = count_query.where(and_(*conditions))
    
    count_result = await db.execute(count_query)
    total = len(count_result.scalars().all())
    
    # Get additional info for each role
    role_infos = []
    for role in roles:
        # Get user count
        user_count_result = await db.execute(
            select(func.count(UserInRole.id)).where(UserInRole.role_id == role.id)
        )
        user_count = user_count_result.scalar() or 0
        
        role_info = RoleInfoResponse(
            id=role.id,
            name=role.name,
            description=role.description,
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at,
            user_count=user_count
        )
        role_infos.append(role_info)
    
    return RoleListResponse(
        items=role_infos,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.post("/", response_model=RoleResponse)
async def create_role(
    role_request: RoleCreateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """Create a new role (admin only)"""
    
    # Check if role name already exists
    result = await db.execute(
        select(Role).where(Role.name == role_request.name)
    )
    existing_role = result.scalar_one_or_none()
    
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Role name already exists"
        )
    
    # Create new role
    role = Role(
        id=f"role_{datetime.utcnow().timestamp()}",
        name=role_request.name,
        description=role_request.description,
        is_active=role_request.is_active,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    db.add(role)
    await db.commit()
    await db.refresh(role)
    
    return RoleResponse.from_orm(role)


@router.get("/{role_id}", response_model=RoleResponse)
async def get_role(
    role_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """Get role by ID (admin only)"""
    
    result = await db.execute(
        select(Role).where(Role.id == role_id)
    )
    role = result.scalar_one_or_none()
    
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    return RoleResponse.from_orm(role)


@router.put("/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: str,
    role_request: RoleUpdateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """Update role (admin only)"""
    
    result = await db.execute(
        select(Role).where(Role.id == role_id)
    )
    role = result.scalar_one_or_none()
    
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # Update fields
    update_data = role_request.dict(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(role, field):
            setattr(role, field, value)
    
    role.updated_at = datetime.utcnow()
    await db.commit()
    await db.refresh(role)
    
    return RoleResponse.from_orm(role)


@router.delete("/{role_id}")
async def delete_role(
    role_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """Delete role (admin only)"""
    
    result = await db.execute(
        select(Role).where(Role.id == role_id)
    )
    role = result.scalar_one_or_none()
    
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # Check if role is assigned to any users
    user_count_result = await db.execute(
        select(func.count(UserInRole.id)).where(UserInRole.role_id == role_id)
    )
    user_count = user_count_result.scalar() or 0
    
    if user_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete role that is assigned to users"
        )
    
    await db.delete(role)
    await db.commit()
    
    return {"message": "Role deleted successfully"}


@router.get("/{role_id}/users", response_model=List[dict])
async def get_role_users(
    role_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """Get users assigned to a role (admin only)"""
    
    # Verify role exists
    result = await db.execute(
        select(Role).where(Role.id == role_id)
    )
    role = result.scalar_one_or_none()
    
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # Get users with this role
    result = await db.execute(
        select(User)
        .join(UserInRole, User.id == UserInRole.user_id)
        .where(UserInRole.role_id == role_id)
    )
    users = result.scalars().all()
    
    return [
        {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "is_active": user.is_active,
            "created_at": user.created_at
        }
        for user in users
    ]

