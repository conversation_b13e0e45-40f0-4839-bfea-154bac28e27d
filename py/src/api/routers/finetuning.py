"""
Fine-tuning API endpoints
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user, require_admin
from models.user import User
from models.finetuning import FineTuningTask, TrainingDataset, FineTuningTaskStatus, TrainingDatasetStatus
from schemas.finetuning import (
    TrainingDatasetResponse,
    TrainingDatasetListResponse,
    CreateDatasetInput,
    UpdateDatasetInput,
    FineTuningTaskResponse,
    FineTuningTaskListResponse,
    CreateTaskInput,
    StartTaskInput
)

router = APIRouter()


# Dataset endpoints
@router.post("/datasets", response_model=TrainingDatasetResponse)
async def create_dataset(
    input_data: CreateDatasetInput,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Create a new training dataset"""
    
    dataset = TrainingDataset(
        warehouse_id=input_data.warehouse_id,
        user_id=current_user.id,
        name=input_data.name,
        endpoint=input_data.endpoint,
        api_key=input_data.api_key,
        prompt=input_data.prompt,
        model=input_data.model,
        status=TrainingDatasetStatus.NOT_STARTED
    )
    
    db.add(dataset)
    await db.commit()
    await db.refresh(dataset)
    
    return dataset


@router.get("/datasets", response_model=TrainingDatasetListResponse)
async def get_datasets(
    warehouse_id: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get training datasets"""
    
    query = select(TrainingDataset).where(TrainingDataset.user_id == current_user.id)
    
    if warehouse_id:
        query = query.where(TrainingDataset.warehouse_id == warehouse_id)
    
    # Get total count
    count_query = select(func.count(TrainingDataset.id)).where(TrainingDataset.user_id == current_user.id)
    if warehouse_id:
        count_query = count_query.where(TrainingDataset.warehouse_id == warehouse_id)
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # Get paginated results
    query = query.offset((page - 1) * size).limit(size).order_by(TrainingDataset.created_at.desc())
    result = await db.execute(query)
    datasets = result.scalars().all()
    
    return TrainingDatasetListResponse(
        items=datasets,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.get("/datasets/{dataset_id}", response_model=TrainingDatasetResponse)
async def get_dataset(
    dataset_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get a specific training dataset"""
    
    result = await db.execute(
        select(TrainingDataset).where(
            and_(
                TrainingDataset.id == dataset_id,
                TrainingDataset.user_id == current_user.id
            )
        )
    )
    dataset = result.scalar_one_or_none()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )
    
    return dataset


@router.put("/datasets/{dataset_id}", response_model=TrainingDatasetResponse)
async def update_dataset(
    dataset_id: str,
    input_data: UpdateDatasetInput,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Update a training dataset"""
    
    result = await db.execute(
        select(TrainingDataset).where(
            and_(
                TrainingDataset.id == dataset_id,
                TrainingDataset.user_id == current_user.id
            )
        )
    )
    dataset = result.scalar_one_or_none()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )
    
    # Update fields
    if input_data.name is not None:
        dataset.name = input_data.name
    if input_data.endpoint is not None:
        dataset.endpoint = input_data.endpoint
    if input_data.api_key is not None:
        dataset.api_key = input_data.api_key
    if input_data.prompt is not None:
        dataset.prompt = input_data.prompt
    if input_data.model is not None:
        dataset.model = input_data.model
    
    dataset.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(dataset)
    
    return dataset


@router.delete("/datasets/{dataset_id}")
async def delete_dataset(
    dataset_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Delete a training dataset"""
    
    result = await db.execute(
        select(TrainingDataset).where(
            and_(
                TrainingDataset.id == dataset_id,
                TrainingDataset.user_id == current_user.id
            )
        )
    )
    dataset = result.scalar_one_or_none()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )
    
    await db.delete(dataset)
    await db.commit()
    
    return {"message": "Dataset deleted successfully"}


# Task endpoints
@router.post("/tasks", response_model=FineTuningTaskResponse)
async def create_task(
    input_data: CreateTaskInput,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Create a new fine-tuning task"""
    
    task = FineTuningTask(
        warehouse_id=input_data.warehouse_id,
        training_dataset_id=input_data.training_dataset_id,
        document_catalog_id=input_data.document_catalog_id,
        name=input_data.name,
        user_id=current_user.id,
        description=input_data.description,
        status=FineTuningTaskStatus.NOT_STARTED
    )
    
    db.add(task)
    await db.commit()
    await db.refresh(task)
    
    return task


@router.get("/tasks", response_model=FineTuningTaskListResponse)
async def get_tasks(
    warehouse_id: str,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get fine-tuning tasks for a warehouse"""
    
    # Get total count
    count_query = select(func.count(FineTuningTask.id)).where(
        and_(
            FineTuningTask.warehouse_id == warehouse_id,
            FineTuningTask.user_id == current_user.id
        )
    )
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # Get paginated results
    query = select(FineTuningTask).where(
        and_(
            FineTuningTask.warehouse_id == warehouse_id,
            FineTuningTask.user_id == current_user.id
        )
    ).offset((page - 1) * size).limit(size).order_by(FineTuningTask.created_at.desc())
    
    result = await db.execute(query)
    tasks = result.scalars().all()
    
    return FineTuningTaskListResponse(
        items=tasks,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.get("/tasks/{task_id}", response_model=FineTuningTaskResponse)
async def get_task(
    task_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get a specific fine-tuning task"""
    
    result = await db.execute(
        select(FineTuningTask).where(
            and_(
                FineTuningTask.id == task_id,
                FineTuningTask.user_id == current_user.id
            )
        )
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return task


@router.post("/tasks/{task_id}/start", response_model=FineTuningTaskResponse)
async def start_task(
    task_id: str,
    input_data: StartTaskInput,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Start a fine-tuning task"""
    
    result = await db.execute(
        select(FineTuningTask).where(
            and_(
                FineTuningTask.id == task_id,
                FineTuningTask.user_id == current_user.id
            )
        )
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    if task.status != FineTuningTaskStatus.NOT_STARTED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Task is already started or completed"
        )
    
    # Update task status
    task.status = FineTuningTaskStatus.IN_PROGRESS
    task.started_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(task)
    
    return task


@router.post("/tasks/{task_id}/cancel", response_model=FineTuningTaskResponse)
async def cancel_task(
    task_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Cancel a fine-tuning task"""
    
    result = await db.execute(
        select(FineTuningTask).where(
            and_(
                FineTuningTask.id == task_id,
                FineTuningTask.user_id == current_user.id
            )
        )
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    if task.status not in [FineTuningTaskStatus.NOT_STARTED, FineTuningTaskStatus.IN_PROGRESS]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Task cannot be cancelled"
        )
    
    # Update task status
    task.status = FineTuningTaskStatus.CANCELLED
    task.completed_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(task)
    
    return task


@router.delete("/tasks/{task_id}")
async def delete_task(
    task_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Delete a fine-tuning task"""
    
    result = await db.execute(
        select(FineTuningTask).where(
            and_(
                FineTuningTask.id == task_id,
                FineTuningTask.user_id == current_user.id
            )
        )
    )
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    await db.delete(task)
    await db.commit()
    
    return {"message": "Task deleted successfully"}
