"""
Document API endpoints
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user
from models.user import User
from models.document import Document, DocumentCatalog, DocumentFileItem
from schemas.document import (
    DocumentResponse,
    DocumentCatalogResponse,
    DocumentFileItemResponse,
    DocumentListResponse
)
from services.document import DocumentService

router = APIRouter()


@router.get("/", response_model=DocumentListResponse)
async def list_documents(
    warehouse_id: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """List documents with pagination and filtering"""
    
    query = select(Document).options(selectinload(Document.warehouse))
    
    # Add filters
    conditions = []
    
    if warehouse_id:
        conditions.append(Document.warehouse_id == warehouse_id)
    
    if search:
        conditions.append(
            or_(
                Document.description.ilike(f"%{search}%")
            )
        )
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # Add pagination
    offset = (page - 1) * size
    query = query.offset(offset).limit(size)
    
    # Execute query
    result = await db.execute(query)
    documents = result.scalars().all()
    
    # Get total count
    count_query = select(Document)
    if conditions:
        count_query = count_query.where(and_(*conditions))
    
    count_result = await db.execute(count_query)
    total = len(count_result.scalars().all())
    
    return DocumentListResponse(
        items=[DocumentResponse.from_orm(document) for document in documents],
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get document by ID"""
    
    result = await db.execute(
        select(Document)
        .options(
            selectinload(Document.warehouse),
            selectinload(Document.document_catalogs),
            selectinload(Document.document_overviews)
        )
        .where(Document.id == document_id)
    )
    document = result.scalar_one_or_none()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    return DocumentResponse.from_orm(document)


@router.get("/{document_id}/catalogs", response_model=List[DocumentCatalogResponse])
async def get_document_catalogs(
    document_id: str,
    parent_id: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get document catalogs/directories"""
    
    # Verify document exists
    result = await db.execute(
        select(Document).where(Document.id == document_id)
    )
    document = result.scalar_one_or_none()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Get catalogs
    query = select(DocumentCatalog).where(
        and_(
            DocumentCatalog.document_id == document_id,
            DocumentCatalog.is_deleted == False
        )
    )
    
    if parent_id:
        query = query.where(DocumentCatalog.parent_id == parent_id)
    else:
        query = query.where(DocumentCatalog.parent_id.is_(None))
    
    result = await db.execute(query)
    catalogs = result.scalars().all()
    
    return [DocumentCatalogResponse.from_orm(catalog) for catalog in catalogs]


@router.get("/catalogs/{catalog_id}/files", response_model=List[DocumentFileItemResponse])
async def get_catalog_files(
    catalog_id: str,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get files in a catalog"""
    
    # Verify catalog exists
    result = await db.execute(
        select(DocumentCatalog).where(DocumentCatalog.id == catalog_id)
    )
    catalog = result.scalar_one_or_none()
    
    if not catalog:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Catalog not found"
        )
    
    # Get files
    offset = (page - 1) * size
    query = (
        select(DocumentFileItem)
        .options(selectinload(DocumentFileItem.file_item_sources))
        .where(DocumentFileItem.document_catalog_id == catalog_id)
        .offset(offset)
        .limit(size)
    )
    
    result = await db.execute(query)
    files = result.scalars().all()
    
    return [DocumentFileItemResponse.from_orm(file) for file in files]


@router.get("/files/{file_id}", response_model=DocumentFileItemResponse)
async def get_file(
    file_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get file by ID"""
    
    result = await db.execute(
        select(DocumentFileItem)
        .options(
            selectinload(DocumentFileItem.document_catalog),
            selectinload(DocumentFileItem.file_item_sources),
            selectinload(DocumentFileItem.i18n_translations)
        )
        .where(DocumentFileItem.id == file_id)
    )
    file = result.scalar_one_or_none()
    
    if not file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    return DocumentFileItemResponse.from_orm(file)


@router.post("/{document_id}/generate-catalog")
async def generate_catalog(
    document_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    document_service: DocumentService = Depends()
):
    """Generate catalog for document using AI"""
    
    result = await document_service.generate_catalog(
        db=db,
        document_id=document_id,
        user=current_user
    )
    
    return {"message": "Catalog generation started", "task_id": result.task_id}


@router.post("/{document_id}/analyze")
async def analyze_document(
    document_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    document_service: DocumentService = Depends()
):
    """Analyze document structure and content"""
    
    analysis = await document_service.analyze_document(
        db=db,
        document_id=document_id,
        user=current_user
    )
    
    return analysis
