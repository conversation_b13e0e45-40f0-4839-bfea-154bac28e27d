"""
MCP (Model Context Protocol) API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from core.database import get_database_session
from core.auth import get_current_user
from models.user import User
from models.mcp import MCP<PERSON>istory, AgentTodo
from schemas.mcp import (
    MCPHistoryResponse,
    MCPHistoryListResponse,
    AgentTodoResponse,
    AgentTodoCreateRequest,
    AgentTodoUpdateRequest
)
from services.mcp import MCPService

router = APIRouter()


@router.get("/history", response_model=MCPHistoryListResponse)
async def get_mcp_history(
    warehouse_id: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get MCP interaction history"""
    
    query = select(MCPHistory).where(MCPHistory.user_id == current_user.id)
    
    if warehouse_id:
        query = query.where(MCPHistory.warehouse_id == warehouse_id)
    
    # Add pagination
    offset = (page - 1) * size
    query = query.offset(offset).limit(size).order_by(MCPHistory.created_at.desc())
    
    # Execute query
    result = await db.execute(query)
    histories = result.scalars().all()
    
    # Get total count
    count_query = select(MCPHistory).where(MCPHistory.user_id == current_user.id)
    if warehouse_id:
        count_query = count_query.where(MCPHistory.warehouse_id == warehouse_id)
    
    count_result = await db.execute(count_query)
    total = len(count_result.scalars().all())
    
    return MCPHistoryListResponse(
        items=[MCPHistoryResponse.from_orm(history) for history in histories],
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.post("/chat")
async def mcp_chat(
    message: str,
    warehouse_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    mcp_service: MCPService = Depends()
):
    """Send message to MCP chat"""
    
    response = await mcp_service.chat(
        db=db,
        user=current_user,
        message=message,
        warehouse_id=warehouse_id,
        context=context or {}
    )
    
    return response


@router.get("/todos", response_model=List[AgentTodoResponse])
async def get_agent_todos(
    warehouse_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get agent todos"""
    
    query = select(AgentTodo).where(AgentTodo.user_id == current_user.id)
    
    if warehouse_id:
        query = query.where(AgentTodo.warehouse_id == warehouse_id)
    
    if status:
        query = query.where(AgentTodo.status == status)
    
    query = query.order_by(AgentTodo.created_at.desc())
    
    result = await db.execute(query)
    todos = result.scalars().all()
    
    return [AgentTodoResponse.from_orm(todo) for todo in todos]


@router.post("/todos", response_model=AgentTodoResponse)
async def create_agent_todo(
    todo_request: AgentTodoCreateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Create agent todo"""
    
    todo = AgentTodo(
        id=f"todo_{datetime.utcnow().timestamp()}",
        user_id=current_user.id,
        warehouse_id=todo_request.warehouse_id,
        title=todo_request.title,
        description=todo_request.description,
        status="pending",
        priority=todo_request.priority or "medium"
    )
    
    db.add(todo)
    await db.commit()
    await db.refresh(todo)
    
    return AgentTodoResponse.from_orm(todo)


@router.put("/todos/{todo_id}", response_model=AgentTodoResponse)
async def update_agent_todo(
    todo_id: str,
    todo_request: AgentTodoUpdateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Update agent todo"""
    
    result = await db.execute(
        select(AgentTodo).where(
            and_(
                AgentTodo.id == todo_id,
                AgentTodo.user_id == current_user.id
            )
        )
    )
    todo = result.scalar_one_or_none()
    
    if not todo:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Todo not found"
        )
    
    # Update fields
    update_data = todo_request.dict(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(todo, field):
            setattr(todo, field, value)
    
    todo.updated_at = datetime.utcnow()
    await db.commit()
    await db.refresh(todo)
    
    return AgentTodoResponse.from_orm(todo)


@router.delete("/todos/{todo_id}")
async def delete_agent_todo(
    todo_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Delete agent todo"""
    
    result = await db.execute(
        select(AgentTodo).where(
            and_(
                AgentTodo.id == todo_id,
                AgentTodo.user_id == current_user.id
            )
        )
    )
    todo = result.scalar_one_or_none()
    
    if not todo:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Todo not found"
        )
    
    await db.delete(todo)
    await db.commit()
    
    return {"message": "Todo deleted successfully"}


@router.post("/tools/{tool_name}")
async def execute_mcp_tool(
    tool_name: str,
    parameters: Dict[str, Any],
    warehouse_id: Optional[str] = None,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    mcp_service: MCPService = Depends()
):
    """Execute MCP tool"""
    
    result = await mcp_service.execute_tool(
        db=db,
        user=current_user,
        tool_name=tool_name,
        parameters=parameters,
        warehouse_id=warehouse_id
    )
    
    return result
