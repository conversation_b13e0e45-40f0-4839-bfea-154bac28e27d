"""
Permission API endpoints
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user, require_admin
from models.user import User
from models.permission import Permission, RolePermission, WarehousePermission
from models.warehouse import Warehouse
from schemas.permission import (
    PermissionResponse,
    WarehousePermissionDto,
    WarehousePermissionDetailDto,
    RolePermissionDto,
    WarehousePermissionTreeDto,
    PermissionListResponse
)

router = APIRouter()


@router.get("/warehouse-tree", response_model=List[WarehousePermissionTreeDto])
async def get_warehouse_permission_tree(
    role_id: Optional[str] = Query(None, description="角色ID（可选，用于显示当前权限配置）"),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取仓库权限树形结构"""
    
    # 获取所有仓库
    warehouse_query = select(Warehouse).order_by(Warehouse.organization_name, Warehouse.name)
    warehouse_result = await db.execute(warehouse_query)
    warehouses = warehouse_result.scalars().all()
    
    # 获取角色的仓库权限
    role_permissions = {}
    if role_id:
        permission_query = select(WarehousePermission).where(WarehousePermission.role_id == role_id)
        permission_result = await db.execute(permission_query)
        permissions = permission_result.scalars().all()
        
        for perm in permissions:
            role_permissions[perm.warehouse_id] = WarehousePermissionDto(
                warehouse_id=perm.warehouse_id,
                is_read_only=perm.is_read_only,
                is_write=perm.is_write,
                is_delete=perm.is_delete
            )
    
    # 构建树形结构
    tree = []
    current_org = None
    org_node = None
    
    for warehouse in warehouses:
        if warehouse.organization_name != current_org:
            if org_node:
                tree.append(org_node)
            
            current_org = warehouse.organization_name
            org_node = WarehousePermissionTreeDto(
                id=f"org_{current_org}",
                name=current_org,
                children=[],
                permissions=None
            )
        
        # 创建仓库节点
        warehouse_node = WarehousePermissionTreeDto(
            id=warehouse.id,
            name=warehouse.name,
            children=[],
            permissions=role_permissions.get(warehouse.id)
        )
        
        if org_node:
            org_node.children.append(warehouse_node)
    
    if org_node:
        tree.append(org_node)
    
    return tree


@router.get("/", response_model=PermissionListResponse)
async def list_permissions(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    resource_type: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取权限列表（管理员）"""
    
    query = select(Permission)
    
    if search:
        query = query.where(
            or_(
                Permission.name.contains(search),
                Permission.code.contains(search),
                Permission.description.contains(search)
            )
        )
    
    if resource_type:
        query = query.where(Permission.resource_type == resource_type)
    
    # 获取总数
    count_query = select(func.count(Permission.id))
    if search:
        count_query = count_query.where(
            or_(
                Permission.name.contains(search),
                Permission.code.contains(search),
                Permission.description.contains(search)
            )
        )
    if resource_type:
        count_query = count_query.where(Permission.resource_type == resource_type)
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 获取分页结果
    query = query.offset((page - 1) * size).limit(size).order_by(Permission.name)
    result = await db.execute(query)
    permissions = result.scalars().all()
    
    return PermissionListResponse(
        items=permissions,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.post("/", response_model=PermissionResponse)
async def create_permission(
    name: str,
    code: str,
    description: Optional[str] = None,
    resource_type: str = "warehouse",
    action: str = "read",
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """创建权限（管理员）"""
    
    # 检查权限代码是否已存在
    existing_query = select(Permission).where(Permission.code == code)
    existing_result = await db.execute(existing_query)
    existing = existing_result.scalar_one_or_none()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Permission code already exists"
        )
    
    permission = Permission(
        name=name,
        code=code,
        description=description,
        resource_type=resource_type,
        action=action
    )
    
    db.add(permission)
    await db.commit()
    await db.refresh(permission)
    
    return permission


@router.get("/{permission_id}", response_model=PermissionResponse)
async def get_permission(
    permission_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取权限详情（管理员）"""
    
    permission_query = select(Permission).where(Permission.id == permission_id)
    permission_result = await db.execute(permission_query)
    permission = permission_result.scalar_one_or_none()
    
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found"
        )
    
    return permission


@router.put("/{permission_id}", response_model=PermissionResponse)
async def update_permission(
    permission_id: str,
    name: Optional[str] = None,
    description: Optional[str] = None,
    resource_type: Optional[str] = None,
    action: Optional[str] = None,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """更新权限（管理员）"""
    
    permission_query = select(Permission).where(Permission.id == permission_id)
    permission_result = await db.execute(permission_query)
    permission = permission_result.scalar_one_or_none()
    
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found"
        )
    
    # 更新字段
    if name is not None:
        permission.name = name
    if description is not None:
        permission.description = description
    if resource_type is not None:
        permission.resource_type = resource_type
    if action is not None:
        permission.action = action
    
    permission.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(permission)
    
    return permission


@router.delete("/{permission_id}")
async def delete_permission(
    permission_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """删除权限（管理员）"""
    
    permission_query = select(Permission).where(Permission.id == permission_id)
    permission_result = await db.execute(permission_query)
    permission = permission_result.scalar_one_or_none()
    
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found"
        )
    
    await db.delete(permission)
    await db.commit()
    
    return {"message": "Permission deleted successfully"}


@router.post("/role-permissions")
async def assign_role_permissions(
    request: RolePermissionDto,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """分配角色权限"""
    
    # 删除现有权限
    delete_query = select(WarehousePermission).where(WarehousePermission.role_id == request.role_id)
    delete_result = await db.execute(delete_query)
    existing_permissions = delete_result.scalars().all()
    
    for perm in existing_permissions:
        await db.delete(perm)
    
    # 添加新权限
    for perm_dto in request.warehouse_permissions:
        permission = WarehousePermission(
            role_id=request.role_id,
            warehouse_id=perm_dto.warehouse_id,
            is_read_only=perm_dto.is_read_only,
            is_write=perm_dto.is_write,
            is_delete=perm_dto.is_delete
        )
        db.add(permission)
    
    await db.commit()
    
    return {"message": "Role permissions updated successfully"}


@router.get("/role-permissions/{role_id}", response_model=List[WarehousePermissionDetailDto])
async def get_role_permissions(
    role_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取角色权限"""
    
    permission_query = select(WarehousePermission, Warehouse).join(
        Warehouse, WarehousePermission.warehouse_id == Warehouse.id
    ).where(WarehousePermission.role_id == role_id)
    
    permission_result = await db.execute(permission_query)
    permissions = permission_result.all()
    
    result = []
    for perm, warehouse in permissions:
        result.append(WarehousePermissionDetailDto(
            warehouse_id=perm.warehouse_id,
            organization_name=warehouse.organization_name or "",
            warehouse_name=warehouse.name,
            is_read_only=perm.is_read_only,
            is_write=perm.is_write,
            is_delete=perm.is_delete
        ))
    
    return result
