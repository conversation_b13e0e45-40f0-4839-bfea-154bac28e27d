"""
Statistics API endpoints
"""

from typing import List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user, require_admin
from models.user import User
from models.statistics import DailyStatistics, AccessRecord
from models.warehouse import Warehouse
from models.document import Document
from schemas.statistics import (
    DailyStatisticsResponse,
    StatisticsSummaryResponse,
    AccessRecordResponse,
    StatisticsListResponse
)

router = APIRouter()


@router.get("/daily", response_model=List[DailyStatisticsResponse])
async def get_daily_statistics(
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    days: int = Query(30, ge=1, le=365, description="天数（默认30天）"),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取每日统计数据（管理员）"""
    
    # 设置日期范围
    if not end_date:
        end_date = datetime.utcnow().date()
    if not start_date:
        start_date = end_date - timedelta(days=days-1)
    
    query = select(DailyStatistics).where(
        and_(
            DailyStatistics.date >= start_date,
            DailyStatistics.date <= end_date
        )
    ).order_by(DailyStatistics.date)
    
    result = await db.execute(query)
    statistics = result.scalars().all()
    
    return statistics


@router.get("/summary", response_model=StatisticsSummaryResponse)
async def get_statistics_summary(
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取统计摘要（管理员）"""
    
    # 获取总用户数
    user_count_query = select(func.count(User.id))
    user_count_result = await db.execute(user_count_query)
    total_users = user_count_result.scalar()
    
    # 获取总仓库数
    warehouse_count_query = select(func.count(Warehouse.id))
    warehouse_count_result = await db.execute(warehouse_count_query)
    total_warehouses = warehouse_count_result.scalar()
    
    # 获取总文档数
    document_count_query = select(func.count(Document.id))
    document_count_result = await db.execute(document_count_query)
    total_documents = document_count_result.scalar()
    
    # 获取今日访问量
    today = datetime.utcnow().date()
    today_views_query = select(func.sum(DailyStatistics.page_views)).where(
        DailyStatistics.date == today
    )
    today_views_result = await db.execute(today_views_query)
    today_views = today_views_result.scalar() or 0
    
    # 获取今日独立访问者
    today_visitors_query = select(func.sum(DailyStatistics.unique_visitors)).where(
        DailyStatistics.date == today
    )
    today_visitors_result = await db.execute(today_visitors_query)
    today_visitors = today_visitors_result.scalar() or 0
    
    # 获取今日活跃用户
    today_active_query = select(func.sum(DailyStatistics.active_users)).where(
        DailyStatistics.date == today
    )
    today_active_result = await db.execute(today_active_query)
    today_active = today_active_result.scalar() or 0
    
    return StatisticsSummaryResponse(
        total_users=total_users,
        total_warehouses=total_warehouses,
        total_documents=total_documents,
        today_views=today_views,
        today_visitors=today_visitors,
        today_active_users=today_active
    )


@router.get("/access-records", response_model=StatisticsListResponse)
async def get_access_records(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    resource_type: Optional[str] = Query(None, description="资源类型"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取访问记录（管理员）"""
    
    query = select(AccessRecord)
    
    if resource_type:
        query = query.where(AccessRecord.resource_type == resource_type)
    
    if user_id:
        query = query.where(AccessRecord.user_id == user_id)
    
    if start_date:
        query = query.where(AccessRecord.created_at >= start_date)
    
    if end_date:
        query = query.where(AccessRecord.created_at <= end_date)
    
    # 获取总数
    count_query = select(func.count(AccessRecord.id))
    if resource_type:
        count_query = count_query.where(AccessRecord.resource_type == resource_type)
    if user_id:
        count_query = count_query.where(AccessRecord.user_id == user_id)
    if start_date:
        count_query = count_query.where(AccessRecord.created_at >= start_date)
    if end_date:
        count_query = count_query.where(AccessRecord.created_at <= end_date)
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 获取分页结果
    query = query.offset((page - 1) * size).limit(size).order_by(desc(AccessRecord.created_at))
    result = await db.execute(query)
    records = result.scalars().all()
    
    return StatisticsListResponse(
        items=records,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.post("/access-record")
async def create_access_record(
    resource_type: str,
    resource_id: str,
    path: str,
    method: str,
    status_code: int,
    response_time: int,
    ip_address: str = "127.0.0.1",
    user_agent: str = "Unknown",
    user_id: Optional[str] = None,
    db: AsyncSession = Depends(get_database_session)
):
    """创建访问记录（内部使用）"""
    
    record = AccessRecord(
        resource_type=resource_type,
        resource_id=resource_id,
        user_id=user_id,
        ip_address=ip_address,
        user_agent=user_agent,
        path=path,
        method=method,
        status_code=status_code,
        response_time=response_time
    )
    
    db.add(record)
    await db.commit()
    
    return {"message": "Access record created successfully"}


@router.get("/popular-resources")
async def get_popular_resources(
    limit: int = Query(10, ge=1, le=100, description="返回数量限制"),
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取热门资源（管理员）"""
    
    start_date = datetime.utcnow() - timedelta(days=days)
    
    query = select(
        AccessRecord.resource_type,
        AccessRecord.resource_id,
        func.count(AccessRecord.id).label('access_count')
    ).where(
        AccessRecord.created_at >= start_date
    ).group_by(
        AccessRecord.resource_type,
        AccessRecord.resource_id
    ).order_by(
        desc('access_count')
    ).limit(limit)
    
    result = await db.execute(query)
    popular_resources = result.all()
    
    return [
        {
            "resource_type": resource_type,
            "resource_id": resource_id,
            "access_count": access_count
        }
        for resource_type, resource_id, access_count in popular_resources
    ]


@router.get("/user-activity")
async def get_user_activity(
    user_id: str,
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(require_admin)
):
    """获取用户活动统计（管理员）"""
    
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # 获取用户访问记录
    access_query = select(AccessRecord).where(
        and_(
            AccessRecord.user_id == user_id,
            AccessRecord.created_at >= start_date
        )
    ).order_by(desc(AccessRecord.created_at))
    
    access_result = await db.execute(access_query)
    access_records = access_result.scalars().all()
    
    # 统计访问类型
    resource_types = {}
    for record in access_records:
        if record.resource_type not in resource_types:
            resource_types[record.resource_type] = 0
        resource_types[record.resource_type] += 1
    
    return {
        "user_id": user_id,
        "total_access": len(access_records),
        "resource_types": resource_types,
        "recent_access": [
            {
                "resource_type": record.resource_type,
                "resource_id": record.resource_id,
                "path": record.path,
                "method": record.method,
                "created_at": record.created_at
            }
            for record in access_records[:10]  # 最近10条记录
        ]
    }
