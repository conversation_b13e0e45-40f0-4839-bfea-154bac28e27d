"""
Document catalog API endpoints
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user
from models.user import User
from models.warehouse import Warehouse, WarehouseStatus
from models.document import Document, DocumentCatalog, DocumentCatalogI18n
from schemas.document_catalog import (
    DocumentCatalogResponse,
    DocumentCatalogListResponse,
    UpdateCatalogRequest,
    UpdateDocumentContentRequest
)

router = APIRouter()


@router.get("/catalogs")
async def get_document_catalogs(
    organization_name: str = Query(..., description="组织名称"),
    name: str = Query(..., description="仓库名称"),
    branch: Optional[str] = Query(None, description="分支名称"),
    language_code: Optional[str] = Query(None, description="语言代码"),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """获取文档目录列表"""
    
    # 查找仓库
    warehouse_query = select(Warehouse).where(
        and_(
            Warehouse.name == name,
            Warehouse.organization_name == organization_name,
            or_(
                branch is None,
                Warehouse.branch == branch
            ),
            or_(
                Warehouse.status == WarehouseStatus.COMPLETED,
                Warehouse.status == WarehouseStatus.PROCESSING
            )
        )
    )
    
    warehouse_result = await db.execute(warehouse_query)
    warehouse = warehouse_result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"仓库不存在，请检查仓库名称和组织名称: {organization_name} {name}"
        )
    
    # 查找文档
    document_query = select(Document).where(
        func.lower(Document.warehouse_id) == func.lower(warehouse.id)
    )
    document_result = await db.execute(document_query)
    document = document_result.scalar_one_or_none()
    
    # 查找文档目录
    catalog_query = select(DocumentCatalog).where(
        and_(
            func.lower(DocumentCatalog.warehouse_id) == func.lower(warehouse.id),
            DocumentCatalog.is_deleted == False
        )
    ).options(selectinload(DocumentCatalog.i18n_translations))
    
    catalog_result = await db.execute(catalog_query)
    document_catalogs = catalog_result.scalars().all()
    
    # 构建目录树
    catalog_dict = {catalog.id: catalog for catalog in document_catalogs}
    root_catalogs = []
    
    for catalog in document_catalogs:
        if catalog.parent_id is None:
            root_catalogs.append(catalog)
        else:
            parent = catalog_dict.get(catalog.parent_id)
            if parent:
                if not hasattr(parent, 'children'):
                    parent.children = []
                parent.children.append(catalog)
    
    return {
        "warehouse": {
            "id": warehouse.id,
            "name": warehouse.name,
            "organization_name": warehouse.organization_name,
            "branch": warehouse.branch,
            "status": warehouse.status
        },
        "document": {
            "id": document.id if document else None,
            "description": document.description if document else None
        } if document else None,
        "catalogs": root_catalogs
    }


@router.get("/document/{owner}/{name}")
async def get_document_by_id(
    owner: str,
    name: str,
    branch: Optional[str] = Query(None, description="分支名称"),
    path: str = Query(..., description="文档路径"),
    language_code: Optional[str] = Query(None, description="语言代码"),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """根据ID获取文档内容"""
    
    # 查找仓库
    warehouse_query = select(Warehouse).where(
        and_(
            Warehouse.name == name,
            Warehouse.organization_name == owner,
            or_(
                branch is None,
                Warehouse.branch == branch
            )
        )
    )
    
    warehouse_result = await db.execute(warehouse_query)
    warehouse = warehouse_result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"仓库不存在: {owner}/{name}"
        )
    
    # 查找文档目录
    catalog_query = select(DocumentCatalog).where(
        and_(
            func.lower(DocumentCatalog.warehouse_id) == func.lower(warehouse.id),
            DocumentCatalog.name == path,
            DocumentCatalog.is_deleted == False
        )
    ).options(selectinload(DocumentCatalog.i18n_translations))
    
    catalog_result = await db.execute(catalog_query)
    catalog = catalog_result.scalar_one_or_none()
    
    if not catalog:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"文档不存在: {path}"
        )
    
    # 根据语言代码过滤内容
    content = catalog.description
    if language_code and catalog.i18n_translations:
        for translation in catalog.i18n_translations:
            if translation.language_code == language_code:
                content = translation.description
                break
    
    return {
        "id": catalog.id,
        "name": catalog.name,
        "description": content,
        "path": path,
        "language_code": language_code,
        "warehouse_id": warehouse.id
    }


@router.put("/catalogs")
async def update_catalog(
    request: UpdateCatalogRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """更新目录信息"""
    
    # 查找目录
    catalog_query = select(DocumentCatalog).where(
        and_(
            DocumentCatalog.id == request.catalog_id,
            DocumentCatalog.is_deleted == False
        )
    )
    
    catalog_result = await db.execute(catalog_query)
    catalog = catalog_result.scalar_one_or_none()
    
    if not catalog:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="目录不存在"
        )
    
    # 更新目录信息
    if request.name is not None:
        catalog.name = request.name
    if request.description is not None:
        catalog.description = request.description
    
    catalog.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(catalog)
    
    return {"success": True, "message": "目录更新成功"}


@router.put("/content")
async def update_document_content(
    request: UpdateDocumentContentRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """更新文档内容"""
    
    # 查找目录
    catalog_query = select(DocumentCatalog).where(
        and_(
            DocumentCatalog.id == request.catalog_id,
            DocumentCatalog.is_deleted == False
        )
    )
    
    catalog_result = await db.execute(catalog_query)
    catalog = catalog_result.scalar_one_or_none()
    
    if not catalog:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="目录不存在"
        )
    
    # 更新内容
    if request.content is not None:
        catalog.description = request.content
    
    catalog.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(catalog)
    
    return {"success": True, "message": "文档内容更新成功"}
