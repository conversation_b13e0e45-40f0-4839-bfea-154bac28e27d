"""
Warehouse API endpoints
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status, Query, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

from core.database import get_database_session
from core.auth import get_current_user
from models.user import User
from models.warehouse import Warehouse, WarehouseStatus, WarehouseType
from models.document import Document
from models.minimap import MiniMap
from schemas.warehouse import (
    WarehouseResponse,
    WarehouseCreateRequest,
    WarehouseUpdateRequest,
    WarehouseListResponse
)
from services.warehouse import WarehouseService

router = APIRouter()


@router.get("/WarehouseList", response_model=WarehouseListResponse)
async def get_warehouse_list(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    status: Optional[WarehouseStatus] = Query(None),
    type: Optional[WarehouseType] = Query(None),
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """List warehouses with pagination and filtering"""
    
    query = select(Warehouse)
    
    # Add filters
    conditions = []
    
    if search:
        conditions.append(
            or_(
                Warehouse.name.ilike(f"%{search}%"),
                Warehouse.description.ilike(f"%{search}%"),
                Warehouse.organization_name.ilike(f"%{search}%")
            )
        )
    
    if status:
        conditions.append(Warehouse.status == status)
    
    if type:
        conditions.append(Warehouse.type == type)
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # Add pagination
    offset = (page - 1) * size
    query = query.offset(offset).limit(size)
    
    # Execute query
    result = await db.execute(query)
    warehouses = result.scalars().all()
    
    # Get total count
    count_query = select(Warehouse)
    if conditions:
        count_query = count_query.where(and_(*conditions))
    
    count_result = await db.execute(count_query)
    total = len(count_result.scalars().all())
    
    return WarehouseListResponse(
        items=[WarehouseResponse.from_orm(warehouse) for warehouse in warehouses],
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.post("/", response_model=WarehouseResponse)
async def create_warehouse(
    warehouse_request: WarehouseCreateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    warehouse_service: WarehouseService = Depends()
):
    """Create a new warehouse"""
    
    warehouse = await warehouse_service.create_warehouse(
        db=db,
        user=current_user,
        request=warehouse_request
    )
    
    return WarehouseResponse.from_orm(warehouse)


@router.get("/{warehouse_id}", response_model=WarehouseResponse)
async def get_warehouse(
    warehouse_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get warehouse by ID"""
    
    result = await db.execute(
        select(Warehouse)
        .options(selectinload(Warehouse.documents))
        .where(Warehouse.id == warehouse_id)
    )
    warehouse = result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    return WarehouseResponse.from_orm(warehouse)


@router.put("/{warehouse_id}", response_model=WarehouseResponse)
async def update_warehouse(
    warehouse_id: str,
    warehouse_request: WarehouseUpdateRequest,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    warehouse_service: WarehouseService = Depends()
):
    """Update warehouse"""
    
    warehouse = await warehouse_service.update_warehouse(
        db=db,
        warehouse_id=warehouse_id,
        user=current_user,
        request=warehouse_request
    )
    
    return WarehouseResponse.from_orm(warehouse)


@router.delete("/{warehouse_id}")
async def delete_warehouse(
    warehouse_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    warehouse_service: WarehouseService = Depends()
):
    """Delete warehouse"""
    
    await warehouse_service.delete_warehouse(
        db=db,
        warehouse_id=warehouse_id,
        user=current_user
    )
    
    return {"message": "Warehouse deleted successfully"}


@router.post("/{warehouse_id}/sync")
async def sync_warehouse(
    warehouse_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    warehouse_service: WarehouseService = Depends()
):
    """Trigger warehouse synchronization"""
    
    sync_record = await warehouse_service.sync_warehouse(
        db=db,
        warehouse_id=warehouse_id,
        user=current_user
    )
    
    return {"message": "Warehouse sync started", "sync_id": sync_record.id}


@router.get("/{warehouse_id}/status")
async def get_warehouse_status(
    warehouse_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get warehouse processing status"""
    
    result = await db.execute(
        select(Warehouse).where(Warehouse.id == warehouse_id)
    )
    warehouse = result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    return {
        "warehouse_id": warehouse_id,
        "status": warehouse.status,
        "last_update": warehouse.updated_at
    }


@router.post("/UploadAndSubmitWarehouse")
async def upload_and_submit_warehouse(
    organization: str,
    repository_name: str,
    file_url: Optional[str] = None,
    file: Optional[UploadFile] = None,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    warehouse_service: WarehouseService = Depends()
):
    """Upload and submit warehouse"""
    
    result = await warehouse_service.upload_and_submit_warehouse(
        db=db,
        user=current_user,
        organization=organization,
        repository_name=repository_name,
        file_url=file_url,
        file=file
    )
    
    return result


@router.post("/SubmitWarehouse")
async def submit_warehouse(
    warehouse_input: dict,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    warehouse_service: WarehouseService = Depends()
):
    """Submit warehouse"""
    
    result = await warehouse_service.submit_warehouse(
        db=db,
        user=current_user,
        warehouse_input=warehouse_input
    )
    
    return result


@router.post("/CustomSubmitWarehouse")
async def custom_submit_warehouse(
    custom_input: dict,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user),
    warehouse_service: WarehouseService = Depends()
):
    """Custom submit warehouse"""
    
    result = await warehouse_service.custom_submit_warehouse(
        db=db,
        user=current_user,
        custom_input=custom_input
    )
    
    return result


@router.get("/MiniMap")
async def get_mini_map(
    owner: str,
    name: str,
    branch: Optional[str] = None,
    db: AsyncSession = Depends(get_database_session)
):
    """Get mini map for warehouse"""
    
    result = await db.execute(
        select(Warehouse).where(
            and_(
                Warehouse.organization_name == owner,
                Warehouse.name == name,
                Warehouse.status.in_(["Completed", "Processing"])
            )
        )
    )
    warehouse = result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    # Get mini map data
    mini_map_result = await db.execute(
        select(MiniMap).where(MiniMap.warehouse_id == warehouse.id)
    )
    mini_map = mini_map_result.scalar_one_or_none()
    
    if not mini_map:
        return {"message": "No mini map found", "data": {}}
    
    return {
        "code": 200,
        "message": "Get mini map successfully",
        "data": mini_map.value
    }


@router.get("/FileContent")
async def get_file_content(
    warehouse_id: str,
    path: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Get file content from warehouse"""
    
    # Check warehouse access
    result = await db.execute(
        select(Warehouse).where(Warehouse.id == warehouse_id)
    )
    warehouse = result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    # Get document
    doc_result = await db.execute(
        select(Document).where(Document.warehouse_id == warehouse_id)
    )
    document = doc_result.scalar_one_or_none()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Read file content (simplified)
    try:
        file_path = f"{document.git_path}/{path}"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return {
            "code": 200,
            "message": "Success",
            "data": content
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to read file: {str(e)}"
        )


@router.get("/MarkdownZip")
async def export_markdown_zip(
    warehouse_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Export markdown zip file"""
    
    # Check warehouse access
    result = await db.execute(
        select(Warehouse).where(Warehouse.id == warehouse_id)
    )
    warehouse = result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    # Generate zip file (simplified)
    # This would typically create a zip file with all markdown content
    return {
        "message": "Zip file generation started",
        "warehouse_id": warehouse_id
    }


@router.get("/FileContentLine")
async def get_file_content_line(
    organization_name: str,
    name: str,
    file_path: str,
    db: AsyncSession = Depends(get_database_session)
):
    """Get file content by organization and name"""
    
    # Find warehouse
    result = await db.execute(
        select(Warehouse).where(
            and_(
                Warehouse.organization_name == organization_name,
                Warehouse.name == name
            )
        )
    )
    warehouse = result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    # Get document
    doc_result = await db.execute(
        select(Document).where(Document.warehouse_id == warehouse.id)
    )
    document = doc_result.scalar_one_or_none()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Read file content
    try:
        full_path = f"{document.git_path}/{file_path}"
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return {
            "code": 200,
            "message": "Success",
            "data": content
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to read file: {str(e)}"
        )


@router.get("/BranchList")
async def get_branch_list(
    address: str,
    git_user_name: Optional[str] = None,
    git_password: Optional[str] = None,
    db: AsyncSession = Depends(get_database_session)
):
    """Get Git repository branch list"""
    
    # This would typically call Git API to get branches
    # For now, return mock data
    return {
        "code": 200,
        "message": "Success",
        "data": {
            "branches": ["main", "master", "develop"],
            "default_branch": "main"
        }
    }


@router.put("/WarehouseStatus")
async def update_warehouse_status(
    warehouse_id: str,
    db: AsyncSession = Depends(get_database_session),
    current_user: User = Depends(get_current_user)
):
    """Update warehouse status"""
    
    result = await db.execute(
        select(Warehouse).where(Warehouse.id == warehouse_id)
    )
    warehouse = result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    # Update status to Pending
    warehouse.status = "Pending"
    warehouse.updated_at = datetime.utcnow()
    
    await db.commit()
    
    return {"message": "Warehouse status updated successfully"}


@router.get("/LastWarehouse")
async def get_last_warehouse(
    address: str,
    db: AsyncSession = Depends(get_database_session)
):
    """Get last warehouse by address"""
    
    # Clean address
    address = address.strip().rstrip('/').lower()
    if not address.endswith('.git'):
        address += '.git'
    
    result = await db.execute(
        select(Warehouse).where(Warehouse.address == address)
    )
    warehouse = result.scalar_one_or_none()
    
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Warehouse not found"
        )
    
    return {
        "name": warehouse.name,
        "address": warehouse.address,
        "description": warehouse.description,
        "version": warehouse.version,
        "status": warehouse.status,
        "error": warehouse.error
    }
