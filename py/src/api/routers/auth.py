"""
Authentication API endpoints
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, EmailStr

from core.database import get_database_session
from core.auth import (
    authenticate_user,
    create_access_token,
    get_password_hash,
    get_current_user
)
from models.user import User
from schemas.auth import LoginRequest, LoginResponse, RegisterRequest, UserResponse

router = APIRouter()
security = HTTPBearer()


@router.post("/Login", response_model=LoginResponse)
async def login(
    login_request: LoginRequest,
    db: AsyncSession = Depends(get_database_session)
):
    """User login"""
    user = await authenticate_user(db, login_request.email, login_request.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Update last login time
    user.last_login_at = datetime.utcnow()
    await db.commit()
    
    # Create access token
    access_token = create_access_token(data={"sub": user.email})
    
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user=UserResponse.model_validate(user)
    )


@router.post("/register", response_model=UserResponse)
async def register(
    register_request: RegisterRequest,
    db: AsyncSession = Depends(get_database_session)
):
    """User registration"""
    
    # Check if user already exists
    result = await db.execute(
        select(User).where(User.email == register_request.email)
    )
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Check if username exists
    result = await db.execute(
        select(User).where(User.name == register_request.name)
    )
    existing_username = result.scalar_one_or_none()
    
    if existing_username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already taken"
        )
    
    # Create new user
    hashed_password = get_password_hash(register_request.password)
    
    user = User(
        id=f"user_{datetime.utcnow().timestamp()}",
        name=register_request.name,
        email=register_request.email,
        password=hashed_password,
        is_active=True,
        is_superuser=False
    )
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    return UserResponse.model_validate(user)


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """Get current user information"""
    return UserResponse.model_validate(current_user)


@router.post("/logout")
async def logout():
    """User logout (client-side token removal)"""
    return {"message": "Successfully logged out"}


@router.post("/refresh")
async def refresh_token(
    current_user: User = Depends(get_current_user)
):
    """Refresh access token"""
    access_token = create_access_token(data={"sub": current_user.email})
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }
