"""
OpenDeepWiki Python API
AI-driven code knowledge base system
"""

import os
import sys
import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAP<PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from core.database import get_database_session, init_database
from core.middleware import (
    AccessLogMiddleware,
    AuthenticationMiddleware,
    GlobalExceptionMiddleware
)
from core.auth import get_current_user
from api.routers import (
    auth_router,
    warehouse_router,
    document_router,
    user_router,
    system_router,
    mcp_router,
    role_router,
    finetuning_router,
    menu_router,
    permission_router,
    statistics_router,
    system_setting_router,
    document_catalog_router
)
from services.background import start_background_tasks


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan management"""
    settings = get_settings()
    logger.info("Starting OpenDeepWiki Python API...")
    
    # Initialize database
    await init_database()
    logger.info("Database initialized")
    
    # Start background tasks
    await start_background_tasks()
    logger.info("Background tasks started")
    
    yield
    
    logger.info("Shutting down OpenDeepWiki Python API...")


# Create FastAPI application
app = FastAPI(
    title="OpenDeepWiki API",
    description="AI-driven code knowledge base system",
    version="0.9.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    lifespan=lifespan
)

# Get settings
settings = get_settings()

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(TrustedHostMiddleware, allowed_hosts=settings.allowed_hosts)
app.add_middleware(GlobalExceptionMiddleware)
app.add_middleware(AccessLogMiddleware)
app.add_middleware(AuthenticationMiddleware)

# Mount static files
if settings.static_files_dir and os.path.exists(settings.static_files_dir):
    app.mount("/static", StaticFiles(directory=settings.static_files_dir), name="static")

# Include API routers
app.include_router(auth_router, prefix="/api/Auth", tags=["Authentication"])
app.include_router(warehouse_router, prefix="/api/Warehouse", tags=["Warehouse"])
app.include_router(document_router, prefix="/api/Document", tags=["Document"])
app.include_router(user_router, prefix="/api/User", tags=["User"])
app.include_router(system_router, prefix="/api/System", tags=["System"])
app.include_router(mcp_router, prefix="/api/MCP", tags=["MCP"])
app.include_router(role_router, prefix="/api/Role", tags=["Role"])
app.include_router(finetuning_router, prefix="/api/FineTuning", tags=["FineTuning"])
app.include_router(menu_router, prefix="/api/Menu", tags=["Menu"])
app.include_router(permission_router, prefix="/api/Permission", tags=["Permission"])
app.include_router(statistics_router, prefix="/api/Statistics", tags=["Statistics"])
app.include_router(system_setting_router, prefix="/api/SystemSetting", tags=["SystemSetting"])
app.include_router(document_catalog_router, prefix="/api/DocumentCatalog", tags=["DocumentCatalog"])


@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "OpenDeepWiki Python API", "version": "0.9.0"}


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": "2024-01-01T00:00:00Z"}


@app.get("/api/status")
async def api_status(
    db: AsyncSession = Depends(get_database_session),
    current_user = Depends(get_current_user)
):
    """API status endpoint (requires authentication)"""
    return {
        "status": "running",
        "user": current_user.email if current_user else None,
        "database": "connected"
    }


if __name__ == "__main__":
    import uvicorn
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    settings = get_settings()

    # Run the application
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
