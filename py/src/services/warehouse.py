"""
Warehouse service layer
"""

import asyncio
import logging
import os
import shutil
from datetime import datetime
from typing import Optional, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import HTTPException, status

from models.user import User
from models.warehouse import Warehouse, WarehouseSyncRecord, Sync<PERSON>tatus, SyncTrigger
from models.document import Document, DocumentCatalog, DocumentFileItem
from schemas.warehouse import WarehouseCreateRequest, WarehouseUpdateRequest
from services.git_service import GitService
from services.ai_service import AIService
from services.code_analysis import CodeAnalysisService
from core.config import get_settings

logger = logging.getLogger(__name__)


class WarehouseService:
    """Warehouse business logic service"""
    
    def __init__(self):
        self.settings = get_settings()
        self.git_service = GitService()
        self.ai_service = AIService()
        self.code_analysis = CodeAnalysisService()
    
    async def create_warehouse(
        self,
        db: AsyncSession,
        user: User,
        request: WarehouseCreateRequest
    ) -> Warehouse:
        """Create a new warehouse"""
        
        # 验证仓库地址
        if request.address:
            try:
                repo_info = await self.git_service.get_repository_info(request.address)
                logger.info(f"Repository info: {repo_info}")
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid repository URL: {str(e)}"
                )
        
        warehouse = Warehouse(
            id=f"warehouse_{int(datetime.utcnow().timestamp())}",
            name=request.name,
            description=request.description,
            address=request.address,
            type=request.type,
            branch=request.branch or "main",
            organization_name=request.organization_name
        )
        
        db.add(warehouse)
        await db.commit()
        await db.refresh(warehouse)
        
        # Start initial analysis in background
        asyncio.create_task(self._process_warehouse_complete(warehouse.id, db))
        
        return warehouse
    
    async def update_warehouse(
        self,
        db: AsyncSession,
        warehouse_id: str,
        user: User,
        request: WarehouseUpdateRequest
    ) -> Warehouse:
        """Update warehouse"""
        
        result = await db.execute(
            select(Warehouse).where(Warehouse.id == warehouse_id)
        )
        warehouse = result.scalar_one_or_none()
        
        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Warehouse not found"
            )
        
        # Update fields
        update_data = request.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(warehouse, field):
                setattr(warehouse, field, value)
        
        warehouse.updated_at = datetime.utcnow()
        await db.commit()
        await db.refresh(warehouse)
        
        return warehouse
    
    async def delete_warehouse(
        self,
        db: AsyncSession,
        warehouse_id: str,
        user: User
    ) -> None:
        """Delete warehouse"""
        
        result = await db.execute(
            select(Warehouse).where(Warehouse.id == warehouse_id)
        )
        warehouse = result.scalar_one_or_none()
        
        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Warehouse not found"
            )
        
        await db.delete(warehouse)
        await db.commit()
    
    async def sync_warehouse(
        self,
        db: AsyncSession,
        warehouse_id: str,
        user: User
    ) -> WarehouseSyncRecord:
        """Trigger warehouse synchronization"""
        
        result = await db.execute(
            select(Warehouse).where(Warehouse.id == warehouse_id)
        )
        warehouse = result.scalar_one_or_none()
        
        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Warehouse not found"
            )
        
        # Create sync record
        sync_record = WarehouseSyncRecord(
            id=f"sync_{datetime.utcnow().timestamp()}",
            warehouse_id=warehouse_id,
            status=SyncStatus.PENDING,
            start_time=datetime.utcnow(),
            trigger=SyncTrigger.MANUAL
        )
        
        db.add(sync_record)
        await db.commit()
        await db.refresh(sync_record)
        
        # Start sync in background
        asyncio.create_task(self._sync_warehouse_background(sync_record.id))
        
        return sync_record
    
    async def _process_warehouse_complete(self, warehouse_id: str, db: AsyncSession):
        """完整的仓库处理流程"""
        try:
            logger.info(f"Starting complete warehouse processing: {warehouse_id}")
            
            # 获取仓库信息
            result = await db.execute(select(Warehouse).where(Warehouse.id == warehouse_id))
            warehouse = result.scalar_one_or_none()
            if not warehouse:
                logger.error(f"Warehouse not found: {warehouse_id}")
                return
            
            # 更新状态为处理中
            warehouse.status = "processing"
            await db.commit()
            
            if warehouse.address:
                # 1. 克隆仓库
                local_path = os.path.join(self.settings.upload_dir, "repositories", warehouse_id)
                repo_info = await self.git_service.clone_repository(
                    warehouse.address,
                    local_path,
                    warehouse.branch
                )
                
                # 2. 分析代码
                analysis_results = await self._analyze_repository_files(
                    local_path, 
                    repo_info["files"]
                )
                
                # 3. 生成文档和目录
                await self._generate_warehouse_documentation(
                    db, warehouse, repo_info, analysis_results
                )
            
            # 更新状态为完成
            warehouse.status = "completed"
            warehouse.updated_at = datetime.utcnow()
            await db.commit()
            
            logger.info(f"Warehouse processing completed: {warehouse_id}")
            
        except Exception as e:
            logger.error(f"Warehouse processing failed: {warehouse_id}, error: {e}")
            # 更新状态为失败
            try:
                result = await db.execute(select(Warehouse).where(Warehouse.id == warehouse_id))
                warehouse = result.scalar_one_or_none()
                if warehouse:
                    warehouse.status = "failed"
                    await db.commit()
            except Exception as commit_error:
                logger.error(f"Failed to update warehouse status: {commit_error}")
    
    async def _analyze_repository_files(self, local_path: str, files: list) -> Dict[str, Any]:
        """分析仓库文件"""
        logger.info(f"Analyzing {len(files)} files in repository")
        
        analysis_results = {
            "total_files": len(files),
            "analyzed_files": [],
            "project_analysis": None,
            "languages_detected": set()
        }
        
        # 分析每个文件
        for file_info in files[:50]:  # 限制分析文件数量以避免超时
            try:
                file_path = file_info["path"]
                language = file_info["language"]
                
                if language != "unknown" and file_info["size"] < 100000:  # 限制文件大小
                    content = await self.git_service.get_file_content(local_path, file_path)
                    
                    file_analysis = await self.code_analysis.analyze_file(
                        file_path, content, language
                    )
                    
                    analysis_results["analyzed_files"].append(file_analysis)
                    analysis_results["languages_detected"].add(language)
                    
            except Exception as e:
                logger.warning(f"Failed to analyze file {file_info['path']}: {e}")
        
        # 项目整体分析
        try:
            analysis_results["project_analysis"] = await self.code_analysis.analyze_project(
                local_path, analysis_results["analyzed_files"]
            )
        except Exception as e:
            logger.error(f"Project analysis failed: {e}")
        
        analysis_results["languages_detected"] = list(analysis_results["languages_detected"])
        return analysis_results
    
    async def _generate_warehouse_documentation(
        self, 
        db: AsyncSession, 
        warehouse: Warehouse, 
        repo_info: Dict[str, Any],
        analysis_results: Dict[str, Any]
    ):
        """生成仓库文档和目录结构"""
        try:
            # 创建文档记录
            document = Document(
                id=f"doc_{int(datetime.utcnow().timestamp())}",
                warehouse_id=warehouse.id,
                description=f"AI生成的{warehouse.name}项目文档",
                git_path=repo_info.get("path", ""),
                status="processing"
            )
            
            db.add(document)
            await db.commit()
            await db.refresh(document)
            
            # 生成AI目录结构
            catalog_data = await self.ai_service.generate_catalog(
                repo_info["files"],
                {
                    "name": warehouse.name,
                    "description": warehouse.description,
                    "language": analysis_results.get("project_analysis", {}).get("main_language", "unknown")
                }
            )
            
            # 创建目录结构
            await self._create_catalog_structure(
                db, document.id, warehouse.id, catalog_data.get("catalog", {})
            )
            
            # 生成项目文档
            if analysis_results["analyzed_files"]:
                documentation = await self.ai_service.generate_documentation(
                    [
                        {
                            "path": f["file_path"],
                            "language": f["language"],
                            "content": await self.git_service.get_file_content(
                                repo_info["path"], f["file_path"]
                            ) if f["file_size"] < 10000 else f"# {f['file_path']}\n\n文件过大，未包含内容"
                        }
                        for f in analysis_results["analyzed_files"][:10]  # 限制文件数量
                    ],
                    warehouse.name,
                    warehouse.description
                )
                
                # 保存文档内容到文件项
                readme_item = DocumentFileItem(
                    id=f"file_{int(datetime.utcnow().timestamp())}",
                    title="项目文档",
                    description="AI生成的项目技术文档",
                    document_catalog_id=document.id,
                    meta_data={"type": "documentation", "generated": True}
                )
                
                db.add(readme_item)
                await db.commit()
            
            # 更新文档状态
            document.status = "completed"
            await db.commit()
            
            logger.info(f"Documentation generated for warehouse: {warehouse.id}")
            
        except Exception as e:
            logger.error(f"Documentation generation failed: {e}")
    
    async def _create_catalog_structure(
        self, 
        db: AsyncSession, 
        document_id: str, 
        warehouse_id: str, 
        catalog_data: Dict[str, Any],
        parent_id: Optional[str] = None
    ):
        """递归创建目录结构"""
        try:
            if not catalog_data or "name" not in catalog_data:
                return
            
            # 创建目录项
            catalog = DocumentCatalog(
                id=f"catalog_{int(datetime.utcnow().timestamp())}_{catalog_data['name']}",
                name=catalog_data["name"],
                description=catalog_data.get("description", ""),
                parent_id=parent_id,
                warehouse_id=warehouse_id,
                document_id=document_id
            )
            
            db.add(catalog)
            await db.commit()
            await db.refresh(catalog)
            
            # 处理子目录
            children = catalog_data.get("children", [])
            for child in children:
                if child.get("type") == "folder":
                    await self._create_catalog_structure(
                        db, document_id, warehouse_id, child, catalog.id
                    )
                elif child.get("type") == "file":
                    # 创建文件项
                    file_item = DocumentFileItem(
                        id=f"file_{int(datetime.utcnow().timestamp())}_{child['name']}",
                        title=child["name"],
                        description=f"文件: {child.get('path', child['name'])}",
                        document_catalog_id=catalog.id,
                        meta_data={
                            "path": child.get("path", ""),
                            "language": child.get("language", ""),
                            "type": "file"
                        }
                    )
                    db.add(file_item)
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"Failed to create catalog structure: {e}")
    
    async def _sync_warehouse_background(self, sync_record_id: str):
        """Background warehouse synchronization"""
        try:
            logger.info(f"Starting warehouse sync: {sync_record_id}")
            
            # 使用新的数据库会话
            from core.database import get_database_session
            
            async with get_database_session() as db:
                # 获取同步记录
                result = await db.execute(
                    select(WarehouseSyncRecord).where(WarehouseSyncRecord.id == sync_record_id)
                )
                sync_record = result.scalar_one_or_none()
                if not sync_record:
                    logger.error(f"Sync record not found: {sync_record_id}")
                    return
                
                # 更新状态为运行中
                sync_record.status = SyncStatus.RUNNING
                await db.commit()
                
                # 获取仓库信息
                result = await db.execute(
                    select(Warehouse).where(Warehouse.id == sync_record.warehouse_id)
                )
                warehouse = result.scalar_one_or_none()
                if not warehouse:
                    raise Exception("Warehouse not found")
                
                if warehouse.address:
                    # 拉取最新更改
                    local_path = os.path.join(self.settings.upload_dir, "repositories", warehouse.id)
                    
                    if os.path.exists(local_path):
                        changes = await self.git_service.pull_repository(local_path)
                        
                        # 更新同步记录
                        sync_record.from_version = changes["from_commit"]
                        sync_record.to_version = changes["to_commit"]
                        sync_record.file_count = len(changes.get("changed_files", []))
                        sync_record.updated_file_count = len([
                            f for f in changes.get("changed_files", []) 
                            if f["change_type"] in ["M", "T"]
                        ])
                        sync_record.added_file_count = len([
                            f for f in changes.get("changed_files", []) 
                            if f["change_type"] == "A"
                        ])
                        sync_record.deleted_file_count = len([
                            f for f in changes.get("changed_files", []) 
                            if f["change_type"] == "D"
                        ])
                        
                        if changes["has_changes"]:
                            # 重新分析更改的文件
                            await self._process_warehouse_changes(db, warehouse, changes)
                    else:
                        # 仓库不存在，重新克隆
                        await self._process_warehouse_complete(warehouse.id, db)
                
                # 更新同步状态为成功
                sync_record.status = SyncStatus.SUCCESS
                sync_record.end_time = datetime.utcnow()
                await db.commit()
                
                logger.info(f"Warehouse sync completed: {sync_record_id}")
                
        except Exception as e:
            logger.error(f"Warehouse sync failed: {sync_record_id}, error: {e}")
            
            # 更新同步状态为失败
            try:
                async with get_database_session() as db:
                    result = await db.execute(
                        select(WarehouseSyncRecord).where(WarehouseSyncRecord.id == sync_record_id)
                    )
                    sync_record = result.scalar_one_or_none()
                    if sync_record:
                        sync_record.status = SyncStatus.FAILED
                        sync_record.end_time = datetime.utcnow()
                        sync_record.error_message = str(e)
                        await db.commit()
            except Exception as commit_error:
                logger.error(f"Failed to update sync record status: {commit_error}")
    
    async def _process_warehouse_changes(
        self, 
        db: AsyncSession, 
        warehouse: Warehouse, 
        changes: Dict[str, Any]
    ):
        """处理仓库更改"""
        try:
            logger.info(f"Processing changes for warehouse {warehouse.id}")
            
            # 如果有新的提交，可以重新生成部分文档
            if changes.get("new_commits"):
                logger.info(f"Found {len(changes['new_commits'])} new commits")
                
                # 这里可以实现增量更新逻辑
                # 例如：只重新分析修改的文件，更新相关文档等
                
        except Exception as e:
            logger.error(f"Failed to process warehouse changes: {e}")
