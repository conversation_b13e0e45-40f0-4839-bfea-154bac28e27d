"""
Git集成服务 - 支持GitHub, GitLab, Gitee
"""

import asyncio
import os
import logging
import shutil
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pathlib import Path

import git
from github import Github
import gitlab
import httpx

from core.config import get_settings

logger = logging.getLogger(__name__)


class GitService:
    """Git仓库管理服务"""
    
    def __init__(self):
        self.settings = get_settings()
        self._setup_clients()
    
    def _setup_clients(self):
        """初始化Git客户端"""
        # GitHub客户端
        if self.settings.github_token:
            self.github = Github(self.settings.github_token)
        
        # GitLab客户端
        if self.settings.gitlab_token:
            self.gitlab = gitlab.Gitlab("https://gitlab.com", private_token=self.settings.gitlab_token)
        
        # Gitee客户端需要通过API调用
        self.gitee_token = self.settings.gitee_token
    
    async def clone_repository(
        self,
        repo_url: str,
        local_path: str,
        branch: str = "main",
        depth: int = 1
    ) -> Dict[str, Any]:
        """克隆仓库到本地"""
        try:
            # 确保本地路径存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # 如果目录已存在，先删除
            if os.path.exists(local_path):
                shutil.rmtree(local_path)
            
            # 克隆仓库
            logger.info(f"Cloning repository {repo_url} to {local_path}")
            
            # 添加认证信息到URL
            authenticated_url = self._add_auth_to_url(repo_url)
            
            repo = git.Repo.clone_from(
                authenticated_url,
                local_path,
                branch=branch,
                depth=depth
            )
            
            # 获取仓库信息
            repo_info = {
                "path": local_path,
                "branch": branch,
                "commit_count": len(list(repo.iter_commits())),
                "last_commit": {
                    "sha": repo.head.commit.hexsha,
                    "message": repo.head.commit.message.strip(),
                    "author": str(repo.head.commit.author),
                    "date": repo.head.commit.committed_datetime.isoformat()
                },
                "files": self._scan_repository_files(local_path)
            }
            
            return repo_info
            
        except Exception as e:
            logger.error(f"Failed to clone repository {repo_url}: {e}")
            raise
    
    def _add_auth_to_url(self, repo_url: str) -> str:
        """为仓库URL添加认证信息"""
        if "github.com" in repo_url and self.settings.github_token:
            return repo_url.replace("https://", f"https://{self.settings.github_token}@")
        elif "gitlab.com" in repo_url and self.settings.gitlab_token:
            return repo_url.replace("https://", f"https://oauth2:{self.settings.gitlab_token}@")
        elif "gitee.com" in repo_url and self.gitee_token:
            return repo_url.replace("https://", f"https://{self.gitee_token}@")
        return repo_url
    
    async def pull_repository(self, local_path: str) -> Dict[str, Any]:
        """拉取仓库最新更改"""
        try:
            repo = git.Repo(local_path)
            
            # 记录拉取前的commit
            old_commit = repo.head.commit.hexsha
            
            # 拉取最新更改
            origin = repo.remotes.origin
            origin.pull()
            
            # 记录拉取后的commit
            new_commit = repo.head.commit.hexsha
            
            # 获取更改信息
            changes = {
                "from_commit": old_commit,
                "to_commit": new_commit,
                "has_changes": old_commit != new_commit,
                "new_commits": [],
                "changed_files": []
            }
            
            if changes["has_changes"]:
                # 获取新提交
                commits = list(repo.iter_commits(f"{old_commit}..{new_commit}"))
                changes["new_commits"] = [
                    {
                        "sha": commit.hexsha,
                        "message": commit.message.strip(),
                        "author": str(commit.author),
                        "date": commit.committed_datetime.isoformat()
                    }
                    for commit in commits
                ]
                
                # 获取更改的文件
                diff = repo.commit(old_commit).diff(repo.commit(new_commit))
                changes["changed_files"] = [
                    {
                        "path": item.a_path or item.b_path,
                        "change_type": item.change_type,
                        "additions": getattr(item, 'additions', 0) if hasattr(item, 'additions') else 0,
                        "deletions": getattr(item, 'deletions', 0) if hasattr(item, 'deletions') else 0
                    }
                    for item in diff
                ]
            
            return changes
            
        except Exception as e:
            logger.error(f"Failed to pull repository {local_path}: {e}")
            raise
    
    def _scan_repository_files(self, repo_path: str) -> List[Dict[str, Any]]:
        """扫描仓库文件"""
        files = []
        repo_path = Path(repo_path)
        
        # 忽略的文件和目录
        ignore_patterns = {
            ".git", ".gitignore", "node_modules", "__pycache__", 
            ".venv", "venv", ".env", "*.pyc", "*.log", ".DS_Store"
        }
        
        for file_path in repo_path.rglob("*"):
            if file_path.is_file():
                # 检查是否应该忽略
                relative_path = file_path.relative_to(repo_path)
                if any(pattern in str(relative_path) for pattern in ignore_patterns):
                    continue
                
                # 获取文件信息
                try:
                    file_info = {
                        "path": str(relative_path),
                        "name": file_path.name,
                        "size": file_path.stat().st_size,
                        "extension": file_path.suffix,
                        "language": self._detect_language(file_path),
                        "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                    }
                    files.append(file_info)
                except Exception as e:
                    logger.warning(f"Failed to process file {file_path}: {e}")
        
        return files
    
    def _detect_language(self, file_path: Path) -> str:
        """检测文件编程语言"""
        extension_map = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".jsx": "javascript",
            ".tsx": "typescript",
            ".java": "java",
            ".cs": "csharp",
            ".cpp": "cpp",
            ".c": "c",
            ".h": "c",
            ".hpp": "cpp",
            ".go": "go",
            ".rs": "rust",
            ".php": "php",
            ".rb": "ruby",
            ".swift": "swift",
            ".kt": "kotlin",
            ".dart": "dart",
            ".scala": "scala",
            ".r": "r",
            ".sql": "sql",
            ".sh": "shell",
            ".bash": "shell",
            ".zsh": "shell",
            ".yaml": "yaml",
            ".yml": "yaml",
            ".json": "json",
            ".xml": "xml",
            ".html": "html",
            ".css": "css",
            ".scss": "scss",
            ".sass": "sass",
            ".md": "markdown",
            ".txt": "text",
            ".dockerfile": "dockerfile",
            ".makefile": "makefile"
        }
        
        extension = file_path.suffix.lower()
        
        # 特殊文件名检查
        if file_path.name.lower() in ["dockerfile", "makefile", "rakefile"]:
            return file_path.name.lower()
        
        return extension_map.get(extension, "unknown")
    
    async def get_repository_info(self, repo_url: str) -> Dict[str, Any]:
        """获取远程仓库信息"""
        try:
            if "github.com" in repo_url:
                return await self._get_github_info(repo_url)
            elif "gitlab.com" in repo_url:
                return await self._get_gitlab_info(repo_url)
            elif "gitee.com" in repo_url:
                return await self._get_gitee_info(repo_url)
            else:
                raise ValueError(f"Unsupported repository platform: {repo_url}")
        except Exception as e:
            logger.error(f"Failed to get repository info for {repo_url}: {e}")
            raise
    
    async def _get_github_info(self, repo_url: str) -> Dict[str, Any]:
        """获取GitHub仓库信息"""
        # 解析仓库路径
        parts = repo_url.replace("https://github.com/", "").replace(".git", "").split("/")
        if len(parts) < 2:
            raise ValueError("Invalid GitHub URL")
        
        owner, repo = parts[0], parts[1]
        
        try:
            repository = self.github.get_repo(f"{owner}/{repo}")
            
            return {
                "name": repository.name,
                "full_name": repository.full_name,
                "description": repository.description or "",
                "language": repository.language or "unknown",
                "stars": repository.stargazers_count,
                "forks": repository.forks_count,
                "size": repository.size,
                "created_at": repository.created_at.isoformat(),
                "updated_at": repository.updated_at.isoformat(),
                "default_branch": repository.default_branch,
                "clone_url": repository.clone_url,
                "ssh_url": repository.ssh_url,
                "topics": repository.get_topics(),
                "license": repository.license.name if repository.license else None,
                "is_private": repository.private,
                "owner": {
                    "login": repository.owner.login,
                    "type": repository.owner.type
                }
            }
        except Exception as e:
            logger.error(f"GitHub API error: {e}")
            raise
    
    async def _get_gitlab_info(self, repo_url: str) -> Dict[str, Any]:
        """获取GitLab仓库信息"""
        # 解析仓库路径
        parts = repo_url.replace("https://gitlab.com/", "").replace(".git", "")
        
        try:
            project = self.gitlab.projects.get(parts)
            
            return {
                "name": project.name,
                "full_name": project.path_with_namespace,
                "description": project.description or "",
                "language": "unknown",  # GitLab API需要额外查询
                "stars": project.star_count,
                "forks": project.forks_count,
                "size": 0,  # GitLab不直接提供大小信息
                "created_at": project.created_at,
                "updated_at": project.last_activity_at,
                "default_branch": project.default_branch,
                "clone_url": project.http_url_to_repo,
                "ssh_url": project.ssh_url_to_repo,
                "topics": project.tag_list,
                "license": None,  # 需要额外查询
                "is_private": project.visibility == "private",
                "owner": {
                    "login": project.namespace["name"],
                    "type": "user" if project.namespace["kind"] == "user" else "group"
                }
            }
        except Exception as e:
            logger.error(f"GitLab API error: {e}")
            raise
    
    async def _get_gitee_info(self, repo_url: str) -> Dict[str, Any]:
        """获取Gitee仓库信息"""
        # 解析仓库路径
        parts = repo_url.replace("https://gitee.com/", "").replace(".git", "").split("/")
        if len(parts) < 2:
            raise ValueError("Invalid Gitee URL")
        
        owner, repo = parts[0], parts[1]
        
        try:
            async with httpx.AsyncClient() as client:
                headers = {}
                if self.gitee_token:
                    headers["Authorization"] = f"token {self.gitee_token}"
                
                response = await client.get(
                    f"https://gitee.com/api/v5/repos/{owner}/{repo}",
                    headers=headers
                )
                response.raise_for_status()
                data = response.json()
                
                return {
                    "name": data["name"],
                    "full_name": data["full_name"],
                    "description": data.get("description", ""),
                    "language": data.get("language", "unknown"),
                    "stars": data.get("stargazers_count", 0),
                    "forks": data.get("forks_count", 0),
                    "size": data.get("size", 0),
                    "created_at": data["created_at"],
                    "updated_at": data["updated_at"],
                    "default_branch": data["default_branch"],
                    "clone_url": data["clone_url"],
                    "ssh_url": data["ssh_url"],
                    "topics": [],
                    "license": data.get("license", {}).get("name") if data.get("license") else None,
                    "is_private": data["private"],
                    "owner": {
                        "login": data["owner"]["login"],
                        "type": data["owner"]["type"]
                    }
                }
        except Exception as e:
            logger.error(f"Gitee API error: {e}")
            raise
    
    async def get_commit_history(
        self,
        local_path: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取提交历史"""
        try:
            repo = git.Repo(local_path)
            commits = []
            
            for commit in repo.iter_commits(max_count=limit):
                commits.append({
                    "sha": commit.hexsha,
                    "message": commit.message.strip(),
                    "author": {
                        "name": commit.author.name,
                        "email": commit.author.email
                    },
                    "date": commit.committed_datetime.isoformat(),
                    "stats": {
                        "additions": commit.stats.total["insertions"],
                        "deletions": commit.stats.total["deletions"],
                        "files": commit.stats.total["files"]
                    }
                })
            
            return commits
        except Exception as e:
            logger.error(f"Failed to get commit history for {local_path}: {e}")
            raise
    
    async def get_file_content(
        self,
        local_path: str,
        file_path: str,
        encoding: str = "utf-8"
    ) -> str:
        """获取文件内容"""
        try:
            full_path = os.path.join(local_path, file_path)
            
            if not os.path.exists(full_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            with open(full_path, "r", encoding=encoding, errors="ignore") as f:
                return f.read()
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            raise
    
    async def detect_project_type(self, local_path: str) -> Dict[str, Any]:
        """检测项目类型和技术栈"""
        project_info = {
            "type": "unknown",
            "framework": "unknown",
            "language": "unknown",
            "build_tools": [],
            "dependencies": {},
            "entry_points": []
        }
        
        try:
            repo_path = Path(local_path)
            
            # 检查配置文件
            config_files = {
                "package.json": "nodejs",
                "requirements.txt": "python",
                "Pipfile": "python",
                "pyproject.toml": "python",
                "pom.xml": "java",
                "build.gradle": "java",
                "Cargo.toml": "rust",
                "go.mod": "go",
                "composer.json": "php",
                "Gemfile": "ruby",
                ".csproj": "csharp",
                "pubspec.yaml": "dart"
            }
            
            for config_file, language in config_files.items():
                if any(repo_path.glob(f"**/{config_file}")):
                    project_info["language"] = language
                    project_info["type"] = language
                    break
            
            # 检测框架
            if project_info["language"] == "python":
                project_info["framework"] = await self._detect_python_framework(repo_path)
            elif project_info["language"] == "nodejs":
                project_info["framework"] = await self._detect_nodejs_framework(repo_path)
            elif project_info["language"] == "java":
                project_info["framework"] = await self._detect_java_framework(repo_path)
            
            return project_info
            
        except Exception as e:
            logger.error(f"Failed to detect project type: {e}")
            return project_info
    
    async def _detect_python_framework(self, repo_path: Path) -> str:
        """检测Python框架"""
        # 检查requirements.txt或setup.py
        requirements_files = list(repo_path.glob("**/requirements*.txt")) + list(repo_path.glob("**/setup.py"))
        
        frameworks = {
            "django": "Django",
            "flask": "Flask",
            "fastapi": "FastAPI",
            "pyramid": "Pyramid",
            "tornado": "Tornado",
            "starlette": "Starlette"
        }
        
        for req_file in requirements_files:
            try:
                content = req_file.read_text().lower()
                for framework, name in frameworks.items():
                    if framework in content:
                        return name
            except:
                continue
        
        return "unknown"
    
    async def _detect_nodejs_framework(self, repo_path: Path) -> str:
        """检测Node.js框架"""
        package_json = repo_path / "package.json"
        if package_json.exists():
            try:
                import json
                data = json.loads(package_json.read_text())
                dependencies = {**data.get("dependencies", {}), **data.get("devDependencies", {})}
                
                frameworks = {
                    "react": "React",
                    "vue": "Vue",
                    "angular": "Angular",
                    "express": "Express",
                    "next": "Next.js",
                    "nuxt": "Nuxt.js",
                    "svelte": "Svelte"
                }
                
                for framework, name in frameworks.items():
                    if framework in dependencies:
                        return name
            except:
                pass
        
        return "unknown"
    
    async def _detect_java_framework(self, repo_path: Path) -> str:
        """检测Java框架"""
        # 检查pom.xml或build.gradle
        pom_files = list(repo_path.glob("**/pom.xml"))
        gradle_files = list(repo_path.glob("**/build.gradle"))
        
        frameworks = {
            "spring": "Spring",
            "springboot": "Spring Boot",
            "hibernate": "Hibernate",
            "struts": "Struts"
        }
        
        for config_file in pom_files + gradle_files:
            try:
                content = config_file.read_text().lower()
                for framework, name in frameworks.items():
                    if framework in content:
                        return name
            except:
                continue
        
        return "unknown"
