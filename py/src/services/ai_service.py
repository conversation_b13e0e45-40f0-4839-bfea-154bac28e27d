"""
AI服务集成 - Open<PERSON><PERSON>, Anthropic, Semantic Kernel功能
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime

import openai
from anthropic import Anthropic
import tiktoken

from core.config import get_settings

logger = logging.getLogger(__name__)


class AIService:
    """AI服务集成类"""
    
    def __init__(self):
        self.settings = get_settings()
        self._setup_clients()
    
    def _setup_clients(self):
        """初始化AI客户端"""
        # OpenAI客户端
        if self.settings.openai_api_key:
            openai.api_key = self.settings.openai_api_key
            if self.settings.openai_api_base:
                openai.api_base = self.settings.openai_api_base
        
        # Anthropic客户端
        if self.settings.anthropic_api_key:
            self.anthropic = Anthropic(api_key=self.settings.anthropic_api_key)
        
        # Token计数器
        self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def count_tokens(self, text: str) -> int:
        """计算文本token数量"""
        return len(self.encoding.encode(text))
    
    async def generate_completion(
        self,
        prompt: str,
        model: str = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        system_prompt: Optional[str] = None
    ) -> str:
        """生成AI补全"""
        try:
            model = model or self.settings.openai_model
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            if model.startswith("claude"):
                # 使用Anthropic
                response = await self._anthropic_completion(messages, max_tokens, temperature)
            else:
                # 使用OpenAI
                response = await self._openai_completion(messages, model, max_tokens, temperature)
            
            return response
        except Exception as e:
            logger.error(f"AI completion failed: {e}")
            raise
    
    async def _openai_completion(
        self,
        messages: List[Dict[str, str]],
        model: str,
        max_tokens: int,
        temperature: float
    ) -> str:
        """OpenAI补全"""
        response = await openai.ChatCompletion.acreate(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature
        )
        return response.choices[0].message.content
    
    async def _anthropic_completion(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int,
        temperature: float
    ) -> str:
        """Anthropic补全"""
        # 转换消息格式
        system_msg = ""
        user_msg = ""
        
        for msg in messages:
            if msg["role"] == "system":
                system_msg = msg["content"]
            elif msg["role"] == "user":
                user_msg = msg["content"]
        
        response = await self.anthropic.messages.create(
            model="claude-3-sonnet-20240229",
            max_tokens=max_tokens,
            temperature=temperature,
            system=system_msg,
            messages=[{"role": "user", "content": user_msg}]
        )
        
        return response.content[0].text
    
    async def stream_completion(
        self,
        prompt: str,
        model: str = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        system_prompt: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """流式生成AI补全"""
        try:
            model = model or self.settings.openai_model
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = await openai.ChatCompletion.acreate(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True
            )
            
            async for chunk in response:
                if chunk.choices[0].delta.get("content"):
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"AI streaming failed: {e}")
            yield f"Error: {str(e)}"
    
    async def analyze_code(self, code: str, language: str = "auto") -> Dict[str, Any]:
        """分析代码结构和复杂度"""
        system_prompt = """你是一个专业的代码分析师。分析给定的代码并返回JSON格式的分析结果，包括：
        - complexity: 代码复杂度 (low/medium/high)
        - functions: 函数列表
        - classes: 类列表
        - imports: 导入列表
        - description: 代码功能描述
        - quality_score: 代码质量评分 (1-10)
        - suggestions: 改进建议
        """
        
        prompt = f"""请分析以下{language}代码：

```{language}
{code}
```

请返回JSON格式的分析结果。"""
        
        try:
            response = await self.generate_completion(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=0.3
            )
            
            # 尝试解析JSON响应
            import json
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                # 如果不是有效JSON，返回基础分析
                return {
                    "complexity": "medium",
                    "description": response[:200] + "..." if len(response) > 200 else response,
                    "quality_score": 7,
                    "functions": [],
                    "classes": [],
                    "imports": [],
                    "suggestions": ["请手动检查代码质量"]
                }
        except Exception as e:
            logger.error(f"Code analysis failed: {e}")
            return {
                "complexity": "unknown",
                "description": "分析失败",
                "quality_score": 0,
                "error": str(e)
            }
    
    async def generate_documentation(
        self,
        code_files: List[Dict[str, str]],
        project_name: str,
        project_description: str = ""
    ) -> str:
        """生成项目文档"""
        system_prompt = """你是一个专业的技术文档编写专家。基于提供的代码文件生成高质量的项目文档。
        文档应包括：
        1. 项目概述
        2. 架构说明
        3. 主要功能模块
        4. API接口文档
        5. 使用示例
        6. 部署说明
        
        使用Markdown格式，结构清晰，内容详实。"""
        
        # 构建代码上下文
        code_context = f"项目名称: {project_name}\n"
        if project_description:
            code_context += f"项目描述: {project_description}\n\n"
        
        code_context += "代码文件:\n"
        for file_info in code_files[:10]:  # 限制文件数量避免token超限
            code_context += f"\n文件: {file_info.get('path', 'unknown')}\n"
            code_context += f"```{file_info.get('language', '')}\n"
            code_context += file_info.get('content', '')[:2000]  # 限制每个文件内容长度
            code_context += "\n```\n"
        
        prompt = f"请为以下项目生成完整的技术文档:\n\n{code_context}"
        
        try:
            documentation = await self.generate_completion(
                prompt=prompt,
                system_prompt=system_prompt,
                max_tokens=2000,
                temperature=0.5
            )
            return documentation
        except Exception as e:
            logger.error(f"Documentation generation failed: {e}")
            return f"# {project_name}\n\n文档生成失败: {str(e)}"
    
    async def generate_catalog(
        self,
        files: List[Dict[str, str]],
        repository_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成智能目录结构"""
        system_prompt = """你是一个智能代码库分析专家。分析给定的文件列表，生成合理的层次化目录结构。
        返回JSON格式，包括：
        - catalog: 层次化目录结构
        - categories: 文件分类
        - main_modules: 主要模块识别
        - entry_points: 入口文件识别
        """
        
        # 构建文件列表
        file_list = []
        for file_info in files:
            file_list.append({
                "path": file_info.get("path", ""),
                "type": file_info.get("type", "file"),
                "size": file_info.get("size", 0),
                "language": file_info.get("language", "")
            })
        
        prompt = f"""请分析以下代码库并生成智能目录结构：

仓库信息：
- 名称: {repository_info.get('name', '')}
- 描述: {repository_info.get('description', '')}
- 主要语言: {repository_info.get('language', '')}

文件列表：
{json.dumps(file_list[:100], ensure_ascii=False, indent=2)}

请返回JSON格式的目录结构分析。"""
        
        try:
            response = await self.generate_completion(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=0.3,
                max_tokens=1500
            )
            
            import json
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                # 返回基础目录结构
                return {
                    "catalog": self._generate_basic_catalog(file_list),
                    "categories": {"unknown": len(file_list)},
                    "main_modules": [],
                    "entry_points": []
                }
        except Exception as e:
            logger.error(f"Catalog generation failed: {e}")
            return {
                "catalog": self._generate_basic_catalog(file_list),
                "error": str(e)
            }
    
    def _generate_basic_catalog(self, files: List[Dict[str, str]]) -> Dict[str, Any]:
        """生成基础目录结构"""
        catalog = {"name": "root", "type": "folder", "children": []}
        
        # 简单的文件夹分组
        folders = {}
        for file_info in files:
            path = file_info.get("path", "")
            parts = path.split("/")
            
            current = catalog
            for part in parts[:-1]:  # 除了文件名
                if part not in folders:
                    folders[part] = {"name": part, "type": "folder", "children": []}
                    current["children"].append(folders[part])
                current = folders[part]
            
            # 添加文件
            if parts:
                current["children"].append({
                    "name": parts[-1],
                    "type": "file",
                    "language": file_info.get("language", ""),
                    "path": path
                })
        
        return catalog
    
    async def chat_with_context(
        self,
        message: str,
        context: Dict[str, Any],
        history: List[Dict[str, str]] = None
    ) -> str:
        """带上下文的聊天"""
        system_prompt = """你是OpenDeepWiki的智能助手，专门帮助用户理解和分析代码仓库。
        你可以回答关于代码结构、功能、最佳实践等问题。
        基于提供的上下文信息来回答用户问题。"""
        
        # 构建上下文信息
        context_str = ""
        if context.get("warehouse"):
            context_str += f"当前仓库: {context['warehouse'].get('name', '')}\n"
            context_str += f"描述: {context['warehouse'].get('description', '')}\n"
        
        if context.get("files"):
            context_str += f"相关文件: {len(context['files'])}个\n"
        
        if context.get("current_file"):
            context_str += f"当前文件: {context['current_file']}\n"
        
        # 构建对话历史
        messages = []
        if history:
            for h in history[-5:]:  # 只保留最近5轮对话
                messages.append({"role": "user", "content": h.get("message", "")})
                messages.append({"role": "assistant", "content": h.get("response", "")})
        
        full_prompt = f"上下文信息:\n{context_str}\n\n用户问题: {message}"
        
        try:
            response = await self.generate_completion(
                prompt=full_prompt,
                system_prompt=system_prompt,
                temperature=0.7
            )
            return response
        except Exception as e:
            logger.error(f"Chat failed: {e}")
            return f"抱歉，我遇到了一些问题: {str(e)}"
