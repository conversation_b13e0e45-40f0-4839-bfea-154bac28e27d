"""
MCP (Model Context Protocol) service layer
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Optional, Dict, Any, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from models.user import User
from models.mcp import <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentTodo
from models.warehouse import Warehouse
from models.document import Document, DocumentFileItem
from services.ai_service import AIService
from services.git_service import GitService
from services.code_analysis import CodeAnalysisService

logger = logging.getLogger(__name__)


class MCPService:
    """MCP business logic service"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.git_service = GitService()
        self.code_analysis = CodeAnalysisService()
        
        # 注册可用的工具
        self.tools = {
            "analyze_code": self._tool_analyze_code,
            "get_file_content": self._tool_get_file_content,
            "search_files": self._tool_search_files,
            "generate_documentation": self._tool_generate_documentation,
            "explain_function": self._tool_explain_function,
            "suggest_improvements": self._tool_suggest_improvements,
            "create_todo": self._tool_create_todo,
            "get_repository_info": self._tool_get_repository_info,
            "compare_files": self._tool_compare_files,
            "generate_tests": self._tool_generate_tests
        }
    
    async def chat(
        self,
        db: AsyncSession,
        user: User,
        message: str,
        warehouse_id: Optional[str] = None,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Handle MCP chat interaction"""
        
        try:
            # 获取对话历史
            history_result = await db.execute(
                select(MCPHistory)
                .where(MCPHistory.user_id == user.id)
                .order_by(MCPHistory.created_at.desc())
                .limit(5)
            )
            chat_history = [
                {"message": h.message, "response": h.response}
                for h in history_result.scalars().all()
            ]
            
            # 构建上下文
            chat_context = context or {}
            
            # 如果指定了仓库，获取仓库信息
            if warehouse_id:
                warehouse_result = await db.execute(
                    select(Warehouse).where(Warehouse.id == warehouse_id)
                )
                warehouse = warehouse_result.scalar_one_or_none()
                if warehouse:
                    chat_context["warehouse"] = {
                        "name": warehouse.name,
                        "description": warehouse.description,
                        "type": warehouse.type,
                        "language": "unknown"  # 可以从分析结果中获取
                    }
            
            # 使用AI服务生成回复
            response = await self.ai_service.chat_with_context(
                message, chat_context, chat_history
            )
            
            # 检查是否需要执行工具
            tool_call = self._parse_tool_call(message)
            if tool_call:
                tool_result = await self.execute_tool(
                    db, user, tool_call["name"], tool_call["parameters"], warehouse_id
                )
                response += f"\n\n工具执行结果:\n{json.dumps(tool_result, ensure_ascii=False, indent=2)}"
            
            # 保存对话历史
            history = MCPHistory(
                id=f"mcp_{int(datetime.utcnow().timestamp())}",
                user_id=user.id,
                warehouse_id=warehouse_id,
                message=message,
                response=response,
                context=chat_context
            )
            
            db.add(history)
            await db.commit()
            
            return {
                "response": response,
                "context": chat_context,
                "timestamp": datetime.utcnow(),
                "tool_executed": tool_call is not None
            }
            
        except Exception as e:
            logger.error(f"MCP chat failed: {e}")
            error_response = f"抱歉，处理您的请求时遇到了问题: {str(e)}"
            
            # 仍然保存错误的对话记录
            try:
                history = MCPHistory(
                    id=f"mcp_{int(datetime.utcnow().timestamp())}",
                    user_id=user.id,
                    warehouse_id=warehouse_id,
                    message=message,
                    response=error_response,
                    context={"error": str(e)}
                )
                db.add(history)
                await db.commit()
            except:
                pass
            
            return {
                "response": error_response,
                "context": context or {},
                "timestamp": datetime.utcnow(),
                "error": True
            }
    
    def _parse_tool_call(self, message: str) -> Optional[Dict[str, Any]]:
        """解析消息中的工具调用"""
        # 简单的工具调用解析
        message_lower = message.lower()
        
        if "分析代码" in message or "analyze code" in message_lower:
            return {"name": "analyze_code", "parameters": {}}
        elif "获取文件" in message or "get file" in message_lower:
            return {"name": "get_file_content", "parameters": {}}
        elif "搜索文件" in message or "search files" in message_lower:
            return {"name": "search_files", "parameters": {}}
        elif "生成文档" in message or "generate doc" in message_lower:
            return {"name": "generate_documentation", "parameters": {}}
        elif "创建任务" in message or "create todo" in message_lower:
            return {"name": "create_todo", "parameters": {"title": message}}
        elif "仓库信息" in message or "repo info" in message_lower:
            return {"name": "get_repository_info", "parameters": {}}
        
        return None
    
    async def execute_tool(
        self,
        db: AsyncSession,
        user: User,
        tool_name: str,
        parameters: Dict[str, Any],
        warehouse_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Execute MCP tool"""
        
        try:
            logger.info(f"Executing MCP tool: {tool_name} for user: {user.id}")
            
            if tool_name not in self.tools:
                return {
                    "error": f"Unknown tool: {tool_name}",
                    "available_tools": list(self.tools.keys())
                }
            
            # 执行工具
            tool_func = self.tools[tool_name]
            result = await tool_func(db, user, parameters, warehouse_id)
            
            return {
                "tool": tool_name,
                "parameters": parameters,
                "result": result,
                "timestamp": datetime.utcnow(),
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Tool execution failed: {tool_name}, error: {e}")
            return {
                "tool": tool_name,
                "parameters": parameters,
                "error": str(e),
                "timestamp": datetime.utcnow(),
                "success": False
            }
    
    async def _tool_analyze_code(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """分析代码工具"""
        if not warehouse_id:
            return {"error": "需要指定仓库ID"}
        
        try:
            # 获取仓库信息
            warehouse_result = await db.execute(
                select(Warehouse).where(Warehouse.id == warehouse_id)
            )
            warehouse = warehouse_result.scalar_one_or_none()
            if not warehouse:
                return {"error": "仓库不存在"}
            
            # 获取文档信息
            doc_result = await db.execute(
                select(Document).where(Document.warehouse_id == warehouse_id)
            )
            documents = doc_result.scalars().all()
            
            if not documents:
                return {"error": "仓库尚未分析，请先同步仓库"}
            
            return {
                "warehouse": {
                    "name": warehouse.name,
                    "description": warehouse.description,
                    "status": warehouse.status
                },
                "documents_count": len(documents),
                "analysis_available": True
            }
            
        except Exception as e:
            return {"error": f"分析失败: {str(e)}"}
    
    async def _tool_get_file_content(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """获取文件内容工具"""
        file_path = parameters.get("file_path")
        if not file_path or not warehouse_id:
            return {"error": "需要指定文件路径和仓库ID"}
        
        try:
            # 构建本地文件路径
            from core.config import get_settings
            from pathlib import Path
            import os
            settings = get_settings()
            local_path = os.path.join(settings.upload_dir, "repositories", warehouse_id)
            
            content = await self.git_service.get_file_content(local_path, file_path)
            
            # 分析文件
            file_extension = file_path.split(".")[-1] if "." in file_path else ""
            language = self.git_service._detect_language(Path(file_path))
            
            analysis = await self.code_analysis.analyze_file(file_path, content, language)
            
            return {
                "file_path": file_path,
                "content": content[:2000] + "..." if len(content) > 2000 else content,
                "full_length": len(content),
                "language": language,
                "analysis": analysis
            }
            
        except Exception as e:
            return {"error": f"获取文件失败: {str(e)}"}
    
    async def _tool_search_files(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """搜索文件工具"""
        search_term = parameters.get("search_term", "")
        if not warehouse_id:
            return {"error": "需要指定仓库ID"}
        
        try:
            # 获取文档文件项
            from models.document import DocumentCatalog
            result = await db.execute(
                select(DocumentFileItem)
                .join(DocumentCatalog)
                .where(DocumentCatalog.warehouse_id == warehouse_id)
                .where(DocumentFileItem.title.ilike(f"%{search_term}%"))
                .limit(20)
            )
            files = result.scalars().all()
            
            return {
                "search_term": search_term,
                "results": [
                    {
                        "title": f.title,
                        "description": f.description,
                        "path": f.meta_data.get("path", "") if f.meta_data else ""
                    }
                    for f in files
                ]
            }
            
        except Exception as e:
            return {"error": f"搜索失败: {str(e)}"}
    
    async def _tool_generate_documentation(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """生成文档工具"""
        if not warehouse_id:
            return {"error": "需要指定仓库ID"}
        
        try:
            # 获取仓库信息
            warehouse_result = await db.execute(
                select(Warehouse).where(Warehouse.id == warehouse_id)
            )
            warehouse = warehouse_result.scalar_one_or_none()
            if not warehouse:
                return {"error": "仓库不存在"}
            
            # 模拟生成文档
            documentation = await self.ai_service.generate_documentation(
                [],  # 实际使用时应该传入文件信息
                warehouse.name,
                warehouse.description
            )
            
            return {
                "warehouse_name": warehouse.name,
                "documentation": documentation[:1000] + "..." if len(documentation) > 1000 else documentation,
                "full_length": len(documentation)
            }
            
        except Exception as e:
            return {"error": f"生成文档失败: {str(e)}"}
    
    async def _tool_explain_function(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """解释函数工具"""
        function_name = parameters.get("function_name")
        if not function_name:
            return {"error": "需要指定函数名"}
        
        # 使用AI解释函数
        explanation = await self.ai_service.generate_completion(
            f"请解释函数 {function_name} 的作用和实现原理",
            system_prompt="你是一个代码解释专家，请用通俗易懂的语言解释代码功能。"
        )
        
        return {
            "function_name": function_name,
            "explanation": explanation
        }
    
    async def _tool_suggest_improvements(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """建议改进工具"""
        code = parameters.get("code", "")
        
        if not code:
            return {"error": "需要提供代码"}
        
        # 使用AI分析并提供改进建议
        suggestions = await self.ai_service.generate_completion(
            f"请分析以下代码并提供改进建议：\n\n```\n{code}\n```",
            system_prompt="你是一个代码审查专家，请提供具体、可行的代码改进建议。"
        )
        
        return {
            "code": code[:500] + "..." if len(code) > 500 else code,
            "suggestions": suggestions
        }
    
    async def _tool_create_todo(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """创建待办任务工具"""
        title = parameters.get("title", "新任务")
        description = parameters.get("description", "")
        
        # 创建待办任务
        todo = AgentTodo(
            id=f"todo_{int(datetime.utcnow().timestamp())}",
            user_id=user.id,
            warehouse_id=warehouse_id,
            title=title,
            description=description,
            status="pending",
            priority="medium"
        )
        
        db.add(todo)
        await db.commit()
        
        return {
            "todo_id": todo.id,
            "title": title,
            "description": description,
            "status": "created"
        }
    
    async def _tool_get_repository_info(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """获取仓库信息工具"""
        if not warehouse_id:
            return {"error": "需要指定仓库ID"}
        
        try:
            # 获取仓库信息
            warehouse_result = await db.execute(
                select(Warehouse).where(Warehouse.id == warehouse_id)
            )
            warehouse = warehouse_result.scalar_one_or_none()
            if not warehouse:
                return {"error": "仓库不存在"}
            
            # 获取文档统计
            doc_result = await db.execute(
                select(Document).where(Document.warehouse_id == warehouse_id)
            )
            documents = doc_result.scalars().all()
            
            return {
                "warehouse": {
                    "name": warehouse.name,
                    "description": warehouse.description,
                    "type": warehouse.type,
                    "status": warehouse.status,
                    "branch": warehouse.branch,
                    "created_at": warehouse.created_at.isoformat()
                },
                "statistics": {
                    "documents_count": len(documents),
                    "last_updated": warehouse.updated_at.isoformat()
                }
            }
            
        except Exception as e:
            return {"error": f"获取仓库信息失败: {str(e)}"}
    
    async def _tool_compare_files(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """比较文件工具"""
        file1 = parameters.get("file1")
        file2 = parameters.get("file2")
        
        if not file1 or not file2:
            return {"error": "需要指定两个文件路径"}
        
        # 这里可以实现文件比较逻辑
        return {
            "file1": file1,
            "file2": file2,
            "comparison": "文件比较功能正在开发中"
        }
    
    async def _tool_generate_tests(
        self, 
        db: AsyncSession, 
        user: User, 
        parameters: Dict[str, Any], 
        warehouse_id: Optional[str]
    ) -> Dict[str, Any]:
        """生成测试用例工具"""
        code = parameters.get("code", "")
        language = parameters.get("language", "python")
        
        if not code:
            return {"error": "需要提供代码"}
        
        # 使用AI生成测试用例
        test_cases = await self.ai_service.generate_completion(
            f"请为以下{language}代码生成测试用例：\n\n```{language}\n{code}\n```",
            system_prompt="你是一个测试专家，请生成完整、覆盖各种情况的测试用例代码。"
        )
        
        return {
            "code": code[:500] + "..." if len(code) > 500 else code,
            "language": language,
            "test_cases": test_cases
        }
