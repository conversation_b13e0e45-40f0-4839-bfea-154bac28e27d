"""
代码分析服务 - 支持多种编程语言的代码分析
"""

import ast
import re
import logging
from typing import Dict, Any, List, Optional, Set
from pathlib import Path
import json

logger = logging.getLogger(__name__)


class CodeAnalysisService:
    """代码分析服务"""
    
    def __init__(self):
        self.language_parsers = {
            "python": PythonParser(),
            "javascript": JavaScriptParser(),
            "typescript": TypeScriptParser(),
            "java": JavaParser(),
            "csharp": CSharpParser(),
            "go": GoParser(),
            "cpp": CppParser(),
            "c": CParser()
        }
    
    async def analyze_file(self, file_path: str, content: str, language: str) -> Dict[str, Any]:
        """分析单个文件"""
        try:
            parser = self.language_parsers.get(language)
            if not parser:
                return self._basic_analysis(file_path, content, language)
            
            analysis = parser.analyze(content)
            analysis.update({
                "file_path": file_path,
                "language": language,
                "lines_of_code": len(content.splitlines()),
                "file_size": len(content),
                "complexity_score": self._calculate_complexity(analysis)
            })
            
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze file {file_path}: {e}")
            return self._basic_analysis(file_path, content, language)
    
    def _basic_analysis(self, file_path: str, content: str, language: str) -> Dict[str, Any]:
        """基础分析（当没有专门的解析器时）"""
        lines = content.splitlines()
        return {
            "file_path": file_path,
            "language": language,
            "lines_of_code": len(lines),
            "file_size": len(content),
            "functions": [],
            "classes": [],
            "imports": [],
            "complexity_score": 1,
            "comments": self._count_comments(content, language),
            "blank_lines": len([line for line in lines if not line.strip()])
        }
    
    def _count_comments(self, content: str, language: str) -> int:
        """统计注释行数"""
        comment_patterns = {
            "python": [r"#.*", r'""".*?"""', r"'''.*?'''"],
            "javascript": [r"//.*", r"/\*.*?\*/"],
            "java": [r"//.*", r"/\*.*?\*/"],
            "csharp": [r"//.*", r"/\*.*?\*/"],
            "go": [r"//.*", r"/\*.*?\*/"],
            "cpp": [r"//.*", r"/\*.*?\*/"],
            "c": [r"//.*", r"/\*.*?\*/"]
        }
        
        patterns = comment_patterns.get(language, [r"#.*", r"//.*"])
        comment_count = 0
        
        for pattern in patterns:
            comment_count += len(re.findall(pattern, content, re.DOTALL))
        
        return comment_count
    
    def _calculate_complexity(self, analysis: Dict[str, Any]) -> int:
        """计算代码复杂度分数"""
        score = 1
        
        # 基于函数数量
        score += len(analysis.get("functions", [])) * 2
        
        # 基于类数量
        score += len(analysis.get("classes", [])) * 3
        
        # 基于导入数量
        score += len(analysis.get("imports", [])) * 1
        
        # 基于代码行数
        loc = analysis.get("lines_of_code", 0)
        if loc > 1000:
            score += 5
        elif loc > 500:
            score += 3
        elif loc > 100:
            score += 1
        
        return min(score, 10)  # 限制在1-10之间
    
    async def analyze_project(self, project_path: str, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析整个项目"""
        try:
            project_analysis = {
                "total_files": len(files),
                "languages": {},
                "total_lines": 0,
                "total_size": 0,
                "files_by_language": {},
                "complexity_distribution": {"low": 0, "medium": 0, "high": 0},
                "function_count": 0,
                "class_count": 0,
                "architecture": await self._analyze_architecture(project_path, files)
            }
            
            for file_info in files:
                language = file_info.get("language", "unknown")
                
                # 统计语言分布
                if language not in project_analysis["languages"]:
                    project_analysis["languages"][language] = {
                        "count": 0,
                        "lines": 0,
                        "size": 0
                    }
                
                project_analysis["languages"][language]["count"] += 1
                project_analysis["languages"][language]["lines"] += file_info.get("lines_of_code", 0)
                project_analysis["languages"][language]["size"] += file_info.get("file_size", 0)
                
                # 总计
                project_analysis["total_lines"] += file_info.get("lines_of_code", 0)
                project_analysis["total_size"] += file_info.get("file_size", 0)
                project_analysis["function_count"] += len(file_info.get("functions", []))
                project_analysis["class_count"] += len(file_info.get("classes", []))
                
                # 复杂度分布
                complexity = file_info.get("complexity_score", 1)
                if complexity <= 3:
                    project_analysis["complexity_distribution"]["low"] += 1
                elif complexity <= 6:
                    project_analysis["complexity_distribution"]["medium"] += 1
                else:
                    project_analysis["complexity_distribution"]["high"] += 1
            
            # 计算主要语言
            if project_analysis["languages"]:
                main_language = max(
                    project_analysis["languages"].items(),
                    key=lambda x: x[1]["lines"]
                )[0]
                project_analysis["main_language"] = main_language
            
            return project_analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze project: {e}")
            return {"error": str(e)}
    
    async def _analyze_architecture(self, project_path: str, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析项目架构"""
        architecture = {
            "type": "unknown",
            "patterns": [],
            "entry_points": [],
            "test_files": [],
            "config_files": [],
            "documentation": []
        }
        
        try:
            for file_info in files:
                file_path = file_info.get("path", "")
                file_name = Path(file_path).name.lower()
                
                # 识别入口文件
                if file_name in ["main.py", "app.py", "index.js", "main.js", "main.java", "program.cs", "main.go"]:
                    architecture["entry_points"].append(file_path)
                
                # 识别测试文件
                if "test" in file_path.lower() or file_path.endswith(("_test.py", "_test.js", "Test.java", "Tests.cs")):
                    architecture["test_files"].append(file_path)
                
                # 识别配置文件
                if file_name in ["config.py", "settings.py", "package.json", "pom.xml", "build.gradle", "appsettings.json"]:
                    architecture["config_files"].append(file_path)
                
                # 识别文档文件
                if file_path.endswith((".md", ".rst", ".txt")) and "readme" in file_name:
                    architecture["documentation"].append(file_path)
            
            # 检测架构模式
            architecture["patterns"] = self._detect_patterns(files)
            
            # 检测项目类型
            architecture["type"] = self._detect_project_type(files)
            
            return architecture
            
        except Exception as e:
            logger.error(f"Failed to analyze architecture: {e}")
            return architecture
    
    def _detect_patterns(self, files: List[Dict[str, Any]]) -> List[str]:
        """检测架构模式"""
        patterns = []
        file_paths = [f.get("path", "") for f in files]
        
        # MVC模式
        if any("models" in path for path in file_paths) and \
           any("views" in path for path in file_paths) and \
           any("controllers" in path for path in file_paths):
            patterns.append("MVC")
        
        # MVP模式
        if any("presenters" in path for path in file_paths):
            patterns.append("MVP")
        
        # MVVM模式
        if any("viewmodels" in path for path in file_paths):
            patterns.append("MVVM")
        
        # 微服务模式
        if any("services" in path for path in file_paths) and \
           any("api" in path for path in file_paths):
            patterns.append("Microservices")
        
        # 分层架构
        if any("business" in path or "domain" in path for path in file_paths) and \
           any("data" in path or "dal" in path for path in file_paths):
            patterns.append("Layered")
        
        return patterns
    
    def _detect_project_type(self, files: List[Dict[str, Any]]) -> str:
        """检测项目类型"""
        file_paths = [f.get("path", "") for f in files]
        
        # Web应用
        if any("templates" in path or "static" in path for path in file_paths):
            return "web_application"
        
        # API项目
        if any("api" in path or "controllers" in path for path in file_paths):
            return "api"
        
        # 库项目
        if any("lib" in path or "src" in path for path in file_paths) and \
           not any("main" in Path(path).name for path in file_paths):
            return "library"
        
        # 桌面应用
        if any("gui" in path or "ui" in path for path in file_paths):
            return "desktop_application"
        
        return "application"


class PythonParser:
    """Python代码解析器"""
    
    def analyze(self, content: str) -> Dict[str, Any]:
        """分析Python代码"""
        try:
            tree = ast.parse(content)
            
            functions = []
            classes = []
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions.append({
                        "name": node.name,
                        "line": node.lineno,
                        "args": [arg.arg for arg in node.args.args],
                        "docstring": ast.get_docstring(node)
                    })
                elif isinstance(node, ast.ClassDef):
                    classes.append({
                        "name": node.name,
                        "line": node.lineno,
                        "methods": [n.name for n in node.body if isinstance(n, ast.FunctionDef)],
                        "docstring": ast.get_docstring(node)
                    })
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    else:
                        imports.append(node.module or "")
            
            return {
                "functions": functions,
                "classes": classes,
                "imports": list(set(imports))
            }
            
        except SyntaxError as e:
            logger.warning(f"Python syntax error: {e}")
            return {"functions": [], "classes": [], "imports": [], "syntax_error": str(e)}


class JavaScriptParser:
    """JavaScript代码解析器"""
    
    def analyze(self, content: str) -> Dict[str, Any]:
        """分析JavaScript代码"""
        functions = []
        classes = []
        imports = []
        
        # 简单的正则表达式解析
        # 函数定义
        function_pattern = r"function\s+(\w+)\s*\("
        functions.extend([{"name": m.group(1)} for m in re.finditer(function_pattern, content)])
        
        # 箭头函数
        arrow_function_pattern = r"(\w+)\s*=\s*\([^)]*\)\s*=>"
        functions.extend([{"name": m.group(1)} for m in re.finditer(arrow_function_pattern, content)])
        
        # 类定义
        class_pattern = r"class\s+(\w+)"
        classes.extend([{"name": m.group(1)} for m in re.finditer(class_pattern, content)])
        
        # 导入语句
        import_pattern = r"import\s+.*?\s+from\s+['\"]([^'\"]+)['\"]"
        imports.extend([m.group(1) for m in re.finditer(import_pattern, content)])
        
        require_pattern = r"require\(['\"]([^'\"]+)['\"]\)"
        imports.extend([m.group(1) for m in re.finditer(require_pattern, content)])
        
        return {
            "functions": functions,
            "classes": classes,
            "imports": list(set(imports))
        }


class TypeScriptParser(JavaScriptParser):
    """TypeScript代码解析器（继承JavaScript解析器）"""
    
    def analyze(self, content: str) -> Dict[str, Any]:
        """分析TypeScript代码"""
        result = super().analyze(content)
        
        # 添加接口检测
        interface_pattern = r"interface\s+(\w+)"
        interfaces = [{"name": m.group(1)} for m in re.finditer(interface_pattern, content)]
        result["interfaces"] = interfaces
        
        # 添加类型定义检测
        type_pattern = r"type\s+(\w+)\s*="
        types = [{"name": m.group(1)} for m in re.finditer(type_pattern, content)]
        result["types"] = types
        
        return result


class JavaParser:
    """Java代码解析器"""
    
    def analyze(self, content: str) -> Dict[str, Any]:
        """分析Java代码"""
        functions = []
        classes = []
        imports = []
        
        # 方法定义
        method_pattern = r"(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\("
        functions.extend([{"name": m.group(1)} for m in re.finditer(method_pattern, content)])
        
        # 类定义
        class_pattern = r"(?:public\s+)?class\s+(\w+)"
        classes.extend([{"name": m.group(1)} for m in re.finditer(class_pattern, content)])
        
        # 导入语句
        import_pattern = r"import\s+([\w.]+);"
        imports.extend([m.group(1) for m in re.finditer(import_pattern, content)])
        
        return {
            "functions": functions,
            "classes": classes,
            "imports": list(set(imports))
        }


class CSharpParser:
    """C#代码解析器"""
    
    def analyze(self, content: str) -> Dict[str, Any]:
        """分析C#代码"""
        functions = []
        classes = []
        imports = []
        
        # 方法定义
        method_pattern = r"(?:public|private|protected|internal)?\s*(?:static)?\s*\w+\s+(\w+)\s*\("
        functions.extend([{"name": m.group(1)} for m in re.finditer(method_pattern, content)])
        
        # 类定义
        class_pattern = r"(?:public\s+)?class\s+(\w+)"
        classes.extend([{"name": m.group(1)} for m in re.finditer(class_pattern, content)])
        
        # using语句
        using_pattern = r"using\s+([\w.]+);"
        imports.extend([m.group(1) for m in re.finditer(using_pattern, content)])
        
        return {
            "functions": functions,
            "classes": classes,
            "imports": list(set(imports))
        }


class GoParser:
    """Go代码解析器"""
    
    def analyze(self, content: str) -> Dict[str, Any]:
        """分析Go代码"""
        functions = []
        classes = []  # Go没有类，但有结构体
        imports = []
        
        # 函数定义
        function_pattern = r"func\s+(\w+)\s*\("
        functions.extend([{"name": m.group(1)} for m in re.finditer(function_pattern, content)])
        
        # 结构体定义
        struct_pattern = r"type\s+(\w+)\s+struct"
        classes.extend([{"name": m.group(1)} for m in re.finditer(struct_pattern, content)])
        
        # 导入语句
        import_pattern = r"import\s+\"([^\"]+)\""
        imports.extend([m.group(1) for m in re.finditer(import_pattern, content)])
        
        return {
            "functions": functions,
            "classes": classes,
            "imports": list(set(imports))
        }


class CppParser:
    """C++代码解析器"""
    
    def analyze(self, content: str) -> Dict[str, Any]:
        """分析C++代码"""
        functions = []
        classes = []
        imports = []
        
        # 函数定义
        function_pattern = r"(?:\w+\s+)*(\w+)\s*\([^)]*\)\s*{"
        functions.extend([{"name": m.group(1)} for m in re.finditer(function_pattern, content)])
        
        # 类定义
        class_pattern = r"class\s+(\w+)"
        classes.extend([{"name": m.group(1)} for m in re.finditer(class_pattern, content)])
        
        # 包含语句
        include_pattern = r"#include\s*[<\"]([^>\"]+)[>\"]"
        imports.extend([m.group(1) for m in re.finditer(include_pattern, content)])
        
        return {
            "functions": functions,
            "classes": classes,
            "imports": list(set(imports))
        }


class CParser(CppParser):
    """C代码解析器（继承C++解析器，因为语法相似）"""
    pass
