"""
Database configuration and session management
"""

import asyncio
from typing import AsyncGenerator

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker

from core.config import get_settings

# Create database base class
Base = declarative_base()

# Global engine and session maker
async_engine = None
AsyncSessionLocal = None
sync_engine = None
SessionLocal = None


async def init_database():
    """Initialize database connections"""
    global async_engine, AsyncSessionLocal, sync_engine, SessionLocal
    
    settings = get_settings()
    
    # Create async engine for main database operations
    async_engine = create_async_engine(
        settings.database_url,
        echo=settings.database_echo,
        future=True
    )
    
    # Create async session maker
    AsyncSessionLocal = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    # Create sync engine for migrations and initial setup
    # Convert async URL to sync URL for migrations
    sync_url = settings.database_url.replace("+aiosqlite", "").replace("+asyncpg", "+psycopg2")
    sync_engine = create_engine(sync_url, echo=settings.database_echo)
    
    # Create sync session maker
    SessionLocal = sessionmaker(
        sync_engine,
        autocommit=False,
        autoflush=False
    )
    
    # Create tables
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session for dependency injection"""
    if AsyncSessionLocal is None:
        await init_database()
    
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


def get_sync_session():
    """Get synchronous session for migrations"""
    if SessionLocal is None:
        raise RuntimeError("Database not initialized")
    
    session = SessionLocal()
    try:
        return session
    finally:
        session.close()


async def close_database():
    """Close database connections"""
    global async_engine, sync_engine
    
    if async_engine:
        await async_engine.dispose()
    
    if sync_engine:
        sync_engine.dispose()
