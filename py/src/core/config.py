"""
Configuration management for OpenDeepWiki
"""

import os
from typing import List, Optional
from functools import lru_cache

from pydantic_settings import BaseSettings
from pydantic import validator


class Settings(BaseSettings):
    """Application settings"""
    
    # Basic settings
    app_name: str = "OpenDeepWiki"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 5085
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 1440  # 24 hours
    
    # Database
    database_url: str = "sqlite+aiosqlite:///./koalawiki.db"
    database_echo: bool = False
    
    # CORS
    cors_origins: List[str] = ["*"]
    allowed_hosts: List[str] = ["*"]
    
    # File storage
    static_files_dir: Optional[str] = "./static"
    upload_dir: str = "./uploads"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    
    # AI Configuration
    openai_api_key: Optional[str] = None
    openai_api_base: Optional[str] = None
    openai_model: str = "gpt-3.5-turbo"
    anthropic_api_key: Optional[str] = None
    
    # Git Integration
    github_token: Optional[str] = None
    gitlab_token: Optional[str] = None
    gitee_token: Optional[str] = None
    
    # Background tasks
    enable_background_tasks: bool = True
    warehouse_sync_interval: int = 3600  # 1 hour
    
    # Feishu Integration
    feishu_app_id: Optional[str] = None
    feishu_app_secret: Optional[str] = None
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    @validator('allowed_hosts', pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(',')]
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()
