"""
MCP (Model Context Protocol) schemas
"""

from typing import Optional, List, Dict, Any
from datetime import datetime

from pydantic import BaseModel


class MCPHistoryResponse(BaseModel):
    """MCP history response schema"""
    id: str
    user_id: str
    warehouse_id: Optional[str]
    message: str
    response: str
    context: Dict[str, Any]
    created_at: datetime
    
    class Config:
        from_attributes = True


class MCPHistoryListResponse(BaseModel):
    """MCP history list response schema"""
    items: List[MCPHistoryResponse]
    total: int
    page: int
    size: int
    pages: int


class AgentTodoCreateRequest(BaseModel):
    """Agent todo creation request schema"""
    warehouse_id: Optional[str]
    title: str
    description: str = ""
    priority: Optional[str] = "medium"


class AgentTodoUpdateRequest(BaseModel):
    """Agent todo update request schema"""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    priority: Optional[str] = None


class AgentTodoResponse(BaseModel):
    """Agent todo response schema"""
    id: str
    user_id: str
    warehouse_id: Optional[str]
    title: str
    description: str
    status: str
    priority: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
