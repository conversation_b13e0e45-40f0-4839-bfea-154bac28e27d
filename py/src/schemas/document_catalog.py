"""
Document catalog schemas
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel


class DocumentCatalogI18nResponse(BaseModel):
    """Document catalog i18n response schema"""
    id: str
    document_catalog_id: str
    language_code: str
    name: str
    description: str
    
    class Config:
        from_attributes = True


class DocumentCatalogResponse(BaseModel):
    """Document catalog response schema"""
    id: str
    name: str
    description: str
    parent_id: Optional[str] = None
    warehouse_id: str
    document_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    i18n_translations: List[DocumentCatalogI18nResponse] = []
    children: List['DocumentCatalogResponse'] = []
    
    class Config:
        from_attributes = True


class DocumentCatalogListResponse(BaseModel):
    """Document catalog list response schema"""
    warehouse: dict
    document: Optional[dict] = None
    catalogs: List[DocumentCatalogResponse]


class UpdateCatalogRequest(BaseModel):
    """Update catalog request schema"""
    catalog_id: str
    name: Optional[str] = None
    description: Optional[str] = None


class UpdateDocumentContentRequest(BaseModel):
    """Update document content request schema"""
    catalog_id: str
    content: Optional[str] = None
