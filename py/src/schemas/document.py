"""
Document schemas
"""

from typing import Optional, List, Dict, Any
from datetime import datetime

from pydantic import BaseModel


class DocumentResponse(BaseModel):
    """Document response schema"""
    id: str
    warehouse_id: str
    last_update: datetime
    description: str
    like_count: int
    comment_count: int
    git_path: str
    status: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class DocumentListResponse(BaseModel):
    """Document list response schema"""
    items: List[DocumentResponse]
    total: int
    page: int
    size: int
    pages: int


class DocumentCatalogResponse(BaseModel):
    """Document catalog response schema"""
    id: str
    name: str
    description: str
    parent_id: Optional[str]
    warehouse_id: str
    document_id: Optional[str]
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class DocumentFileItemResponse(BaseModel):
    """Document file item response schema"""
    id: str
    title: str
    description: str
    document_catalog_id: str
    meta_data: Optional[Dict[str, Any]]
    extra: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class DocumentFileItemSourceResponse(BaseModel):
    """Document file item source response schema"""
    id: str
    name: str
    document_file_item_id: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class DocumentOverviewResponse(BaseModel):
    """Document overview response schema"""
    id: str
    title: str
    document_id: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class DocumentCommitRecordResponse(BaseModel):
    """Document commit record response schema"""
    id: str
    commit_message: str
    author: str
    warehouse_id: str
    document_id: Optional[str]
    commit_id: str
    created_at: datetime
    
    class Config:
        from_attributes = True
