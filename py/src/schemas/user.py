"""
User schemas
"""

from typing import Optional, List
from datetime import datetime

from pydantic import BaseModel, EmailStr


class UserUpdateRequest(BaseModel):
    """User update request schema"""
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    avatar_url: Optional[str] = None
    is_active: Optional[bool] = None


class UserResponse(BaseModel):
    """User response schema"""
    id: str
    name: str
    email: EmailStr
    is_active: bool
    is_superuser: bool
    avatar_url: Optional[str] = None
    created_at: datetime
    last_login_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """User list response schema"""
    items: List[UserResponse]
    total: int
    page: int
    size: int
    pages: int


class RoleResponse(BaseModel):
    """Role response schema"""
    id: str
    name: str
    description: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class UserRoleAssignRequest(BaseModel):
    """User role assignment request schema"""
    role_id: str
