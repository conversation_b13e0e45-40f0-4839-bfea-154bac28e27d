"""
Warehouse schemas
"""

from typing import Optional, List
from datetime import datetime

from pydantic import BaseModel, HttpUrl

from models.warehouse import WarehouseType, WarehouseStatus


class WarehouseCreateRequest(BaseModel):
    """Warehouse creation request schema"""
    name: str
    description: str = ""
    address: Optional[str] = None
    type: WarehouseType = WarehouseType.GITHUB
    branch: str = "main"
    organization_name: Optional[str] = None


class WarehouseUpdateRequest(BaseModel):
    """Warehouse update request schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    address: Optional[str] = None
    branch: Optional[str] = None
    organization_name: Optional[str] = None


class WarehouseResponse(BaseModel):
    """Warehouse response schema"""
    id: str
    name: str
    description: str
    address: Optional[str]
    type: WarehouseType
    branch: Optional[str]
    organization_name: Optional[str]
    status: WarehouseStatus
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class WarehouseListResponse(BaseModel):
    """Warehouse list response schema"""
    items: List[WarehouseResponse]
    total: int
    page: int
    size: int
    pages: int


class WarehouseSyncRecordResponse(BaseModel):
    """Warehouse sync record response schema"""
    id: str
    warehouse_id: str
    status: str
    start_time: datetime
    end_time: Optional[datetime]
    from_version: Optional[str]
    to_version: Optional[str]
    error_message: Optional[str]
    file_count: Optional[int]
    updated_file_count: Optional[int]
    added_file_count: Optional[int]
    deleted_file_count: Optional[int]
    trigger: str
    created_at: datetime
    
    class Config:
        from_attributes = True
