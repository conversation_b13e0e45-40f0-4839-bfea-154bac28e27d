"""
System setting schemas
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel


class SystemSettingResponse(BaseModel):
    """System setting response schema"""
    id: str
    key: str
    value: Optional[str] = None
    group: str
    value_type: str
    description: Optional[str] = None
    is_sensitive: bool
    requires_restart: bool
    default_value: Optional[str] = None
    order: int
    is_enabled: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class SystemSettingCreateRequest(BaseModel):
    """System setting create request schema"""
    key: str
    value: Optional[str] = None
    group: str
    value_type: str = "string"
    description: Optional[str] = None
    is_sensitive: bool = False
    requires_restart: bool = False
    default_value: Optional[str] = None
    order: int = 0
    is_enabled: bool = True


class SystemSettingUpdateRequest(BaseModel):
    """System setting update request schema"""
    value: Optional[str] = None
    group: Optional[str] = None
    value_type: Optional[str] = None
    description: Optional[str] = None
    is_sensitive: Optional[bool] = None
    requires_restart: Optional[bool] = None
    default_value: Optional[str] = None
    order: Optional[int] = None
    is_enabled: Optional[bool] = None


class SystemSettingListResponse(BaseModel):
    """System setting list response schema"""
    items: List[SystemSettingResponse]
    total: int
    page: int
    size: int
    pages: int
