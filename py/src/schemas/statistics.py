"""
Statistics schemas
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel


class DailyStatisticsResponse(BaseModel):
    """Daily statistics response schema"""
    id: str
    date: datetime
    new_users_count: int
    new_repositories_count: int
    new_documents_count: int
    page_views: int
    unique_visitors: int
    active_users: int
    updated_at: datetime
    
    class Config:
        from_attributes = True


class AccessRecordResponse(BaseModel):
    """Access record response schema"""
    id: str
    resource_type: str
    resource_id: str
    user_id: Optional[str] = None
    ip_address: str
    user_agent: str
    path: str
    method: str
    status_code: int
    response_time: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class StatisticsSummaryResponse(BaseModel):
    """Statistics summary response schema"""
    total_users: int
    total_warehouses: int
    total_documents: int
    today_views: int
    today_visitors: int
    today_active_users: int


class StatisticsListResponse(BaseModel):
    """Statistics list response schema"""
    items: List[AccessRecordResponse]
    total: int
    page: int
    size: int
    pages: int
