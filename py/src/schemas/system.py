"""
System schemas
"""

from typing import Dict, Any, Optional
from datetime import datetime

from pydantic import BaseModel


class SystemHealthResponse(BaseModel):
    """System health response schema"""
    status: str
    timestamp: datetime
    database: str
    version: str


class SystemStatsResponse(BaseModel):
    """System statistics response schema"""
    total_users: int
    active_users: int
    total_warehouses: int
    total_documents: int
    warehouse_by_status: Dict[str, int]
    warehouse_by_type: Dict[str, int]
    timestamp: datetime
