"""
Menu schemas
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel


class MenuItemResponse(BaseModel):
    """Menu item response schema"""
    id: str
    name: str
    path: str
    icon: Optional[str] = None
    order: int
    is_hidden: bool
    required_roles: List[str] = []
    children: List['MenuItemResponse'] = []
    
    class Config:
        from_attributes = True


class UserMenuResponse(BaseModel):
    """User menu response schema"""
    user: Optional[dict] = None
    menus: List[MenuItemResponse] = []


class MenuCreateRequest(BaseModel):
    """Menu create request schema"""
    name: str
    path: str
    icon: Optional[str] = None
    order: int = 0
    is_hidden: bool = False
    parent_id: Optional[str] = None
    required_roles: List[str] = []


class MenuUpdateRequest(BaseModel):
    """Menu update request schema"""
    name: Optional[str] = None
    path: Optional[str] = None
    icon: Optional[str] = None
    order: Optional[int] = None
    is_hidden: Optional[bool] = None
    parent_id: Optional[str] = None
    required_roles: Optional[List[str]] = None
