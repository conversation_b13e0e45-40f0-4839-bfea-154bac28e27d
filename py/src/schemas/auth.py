"""
Authentication schemas
"""

from typing import Optional
from datetime import datetime

from pydantic import BaseModel, EmailStr


class LoginRequest(BaseModel):
    """Login request schema"""
    email: EmailStr
    password: str


class RegisterRequest(BaseModel):
    """Registration request schema"""
    name: str
    email: EmailStr
    password: str


class UserResponse(BaseModel):
    """User response schema"""
    id: str
    name: str
    email: EmailStr
    is_active: bool
    is_superuser: bool
    avatar_url: Optional[str] = None
    created_at: datetime
    last_login_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class LoginResponse(BaseModel):
    """Login response schema"""
    access_token: str
    token_type: str
    user: UserResponse
