"""
Fine-tuning schemas
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel
from models.finetuning import FineTuningTaskStatus, TrainingDatasetStatus


class CreateDatasetInput(BaseModel):
    """Create dataset input schema"""
    warehouse_id: str
    name: str
    endpoint: str
    api_key: str
    prompt: str
    model: str


class UpdateDatasetInput(BaseModel):
    """Update dataset input schema"""
    dataset_id: str
    name: Optional[str] = None
    endpoint: Optional[str] = None
    api_key: Optional[str] = None
    prompt: Optional[str] = None
    model: Optional[str] = None


class TrainingDatasetResponse(BaseModel):
    """Training dataset response schema"""
    id: str
    warehouse_id: str
    user_id: str
    created_at: datetime
    updated_at: datetime
    status: TrainingDatasetStatus
    name: str
    endpoint: str
    api_key: str
    prompt: str
    model: str
    
    class Config:
        from_attributes = True


class CreateTaskInput(BaseModel):
    """Create task input schema"""
    warehouse_id: str
    training_dataset_id: str
    document_catalog_id: str
    name: str
    description: str


class StartTaskInput(BaseModel):
    """Start task input schema"""
    task_id: str


class FineTuningTaskResponse(BaseModel):
    """Fine-tuning task response schema"""
    id: str
    warehouse_id: str
    training_dataset_id: str
    document_catalog_id: str
    name: str
    user_id: str
    description: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: FineTuningTaskStatus
    dataset: str
    error: Optional[str] = None
    original_dataset: Optional[str] = None
    
    class Config:
        from_attributes = True


class FineTuningTaskListResponse(BaseModel):
    """Fine-tuning task list response schema"""
    items: List[FineTuningTaskResponse]
    total: int
    page: int
    size: int
    pages: int


class TrainingDatasetListResponse(BaseModel):
    """Training dataset list response schema"""
    items: List[TrainingDatasetResponse]
    total: int
    page: int
    size: int
    pages: int
