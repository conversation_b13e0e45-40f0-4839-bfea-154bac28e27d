"""
Permission schemas
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel


class PermissionResponse(BaseModel):
    """Permission response schema"""
    id: str
    name: str
    code: str
    description: Optional[str] = None
    resource_type: str
    action: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class WarehousePermissionDto(BaseModel):
    """Warehouse permission DTO"""
    warehouse_id: str
    is_read_only: bool = False
    is_write: bool = False
    is_delete: bool = False


class WarehousePermissionDetailDto(WarehousePermissionDto):
    """Warehouse permission detail DTO"""
    organization_name: str
    warehouse_name: str


class RolePermissionDto(BaseModel):
    """Role permission DTO"""
    role_id: str
    warehouse_permissions: List[WarehousePermissionDto] = []


class WarehousePermissionTreeDto(BaseModel):
    """Warehouse permission tree DTO"""
    id: str
    name: str
    children: List['WarehousePermissionTreeDto'] = []
    permissions: Optional[WarehousePermissionDto] = None


class PermissionListResponse(BaseModel):
    """Permission list response schema"""
    items: List[PermissionResponse]
    total: int
    page: int
    size: int
    pages: int
