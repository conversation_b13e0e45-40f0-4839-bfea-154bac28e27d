"""
Role schemas
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel


class RoleResponse(BaseModel):
    """Role response schema"""
    id: str
    name: str
    description: Optional[str] = None
    is_active: bool = True
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class RoleListResponse(BaseModel):
    """Role list response schema"""
    items: List[RoleResponse]
    total: int
    page: int
    size: int
    pages: int


class RoleCreateRequest(BaseModel):
    """Role create request schema"""
    name: str
    description: Optional[str] = None
    is_active: bool = True


class RoleUpdateRequest(BaseModel):
    """Role update request schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class RoleInfoResponse(BaseModel):
    """Role info response schema"""
    id: str
    name: str
    description: Optional[str] = None
    is_active: bool
    user_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
