# Migration from .NET to Python

## Overview

This document outlines the successful migration of OpenDeepWiki from .NET Core to Python FastAPI.

## Migration Completed ✅

### Core Architecture
- **Web Framework**: ASP.NET Core → FastAPI
- **Language**: C# → Python 3.11+
- **ORM**: Entity Framework Core → SQLAlchemy (async)
- **Authentication**: JWT Bearer tokens (maintained)
- **Database**: Multi-database support (SQLite, PostgreSQL, MySQL)

### Components Migrated

#### 1. Database Models ✅
- `User` and authentication models
- `Warehouse` repository models  
- `Document` and catalog models
- `MCP` (Model Context Protocol) models
- `System` configuration models
- All relationships and foreign keys preserved

#### 2. API Endpoints ✅
- **Authentication**: `/api/auth/*` - Login, register, user management
- **Warehouses**: `/api/warehouse/*` - CRUD operations, sync
- **Documents**: `/api/document/*` - Document management, catalog generation
- **Users**: `/api/user/*` - User management (admin)
- **System**: `/api/system/*` - Health, stats, configuration
- **MCP**: `/api/mcp/*` - AI chat, tools, todos

#### 3. Core Services ✅
- Authentication and authorization
- Warehouse management and sync
- Document processing pipeline
- Background task processing
- MCP integration framework

#### 4. Infrastructure ✅
- Async database operations
- JWT authentication
- CORS and security middleware
- Background task scheduling
- Docker containerization
- Multi-environment configuration

## Key Technology Mappings

| .NET Component | Python Equivalent |
|----------------|-------------------|
| ASP.NET Core | FastAPI |
| Entity Framework | SQLAlchemy |
| Dependency Injection | FastAPI Dependencies |
| IHostedService | asyncio tasks |
| JWT Bearer | python-jose |
| Serilog | Python logging |
| Mapster | Pydantic |
| LibGit2Sharp | GitPython |
| Semantic Kernel | OpenAI/Anthropic SDKs |

## Project Structure

```
py/src/
├── main.py                 # Application entry point
├── core/                   # Core utilities
│   ├── config.py          # Settings management
│   ├── database.py        # Database configuration
│   ├── auth.py            # Authentication
│   └── middleware.py      # Middleware components
├── models/                 # SQLAlchemy models
│   ├── base.py            # Base model classes
│   ├── user.py            # User models
│   ├── warehouse.py       # Warehouse models
│   ├── document.py        # Document models
│   ├── mcp.py             # MCP models
│   └── system.py          # System models
├── schemas/                # Pydantic schemas
│   ├── auth.py            # Auth schemas
│   ├── warehouse.py       # Warehouse schemas
│   ├── document.py        # Document schemas
│   ├── user.py            # User schemas
│   ├── system.py          # System schemas
│   └── mcp.py             # MCP schemas
├── api/routers/           # API route modules
│   ├── auth.py            # Authentication endpoints
│   ├── warehouse.py       # Warehouse endpoints
│   ├── document.py        # Document endpoints
│   ├── user.py            # User endpoints
│   ├── system.py          # System endpoints
│   └── mcp.py             # MCP endpoints
└── services/              # Business logic
    ├── background.py      # Background tasks
    ├── warehouse.py       # Warehouse service
    ├── document.py        # Document service
    └── mcp.py             # MCP service
```

## Configuration

### Environment Variables
All .NET configuration options have been converted to environment variables:

```env
# Database
DATABASE_URL=sqlite+aiosqlite:///./koalawiki.db

# AI Services  
OPENAI_API_KEY=your-key
ANTHROPIC_API_KEY=your-key

# Git Integration
GITHUB_TOKEN=your-token
GITLAB_TOKEN=your-token

# Security
SECRET_KEY=your-secret
JWT_EXPIRE_MINUTES=1440
```

## Running the Application

### Development
```bash
# Setup
cd py
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env

# Run
python src/main.py
```

### Production (Docker)
```bash
cd py
docker-compose up -d
```

### API Documentation
- Swagger UI: `http://localhost:8000/api/docs`
- ReDoc: `http://localhost:8000/api/redoc`

## Features Preserved

✅ **AI-Powered Documentation Generation**
✅ **Multi-Platform Git Integration** (GitHub, GitLab, Gitee)
✅ **Intelligent Code Analysis**
✅ **Model Context Protocol (MCP)**
✅ **RESTful API**
✅ **Multi-Database Support**
✅ **JWT Authentication & Authorization**
✅ **Background Processing**
✅ **Docker Deployment**
✅ **Role-Based Access Control**
✅ **Warehouse Synchronization**
✅ **Document Cataloging**
✅ **System Statistics**

## API Compatibility

The Python API maintains full compatibility with the original .NET API:

- Same endpoint paths and HTTP methods
- Same request/response schemas
- Same authentication mechanism
- Same business logic and features

## Performance Considerations

### Advantages
- **FastAPI**: One of the fastest Python frameworks
- **Async/Await**: Native async support throughout
- **Pydantic**: Fast data validation and serialization
- **SQLAlchemy Async**: Efficient database operations

### Optimizations Applied
- Connection pooling for databases
- Async operations for I/O bound tasks
- Background task processing
- Efficient JSON serialization
- Response compression middleware

## Next Steps

1. **Testing**: Add comprehensive test suite
2. **Monitoring**: Implement metrics and logging
3. **Caching**: Add Redis for caching layer
4. **Documentation**: Expand API documentation
5. **Deployment**: Set up CI/CD pipeline

## Migration Benefits

1. **Modern Stack**: FastAPI is a modern, high-performance framework
2. **Python Ecosystem**: Access to rich AI/ML Python libraries
3. **Developer Experience**: Automatic API documentation, type hints
4. **Performance**: FastAPI offers excellent performance for Python
5. **Maintainability**: Clean, typed code with modern patterns
6. **Extensibility**: Easy to extend with Python's rich ecosystem

The migration successfully preserves all functionality while modernizing the technology stack and improving developer experience.
