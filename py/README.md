# OpenDeepWiki Python API

AI-driven code knowledge base system built with FastAPI.

## Features

- **AI-Powered Documentation**: Automatically generate documentation from code repositories
- **Multi-Platform Git Integration**: Support for GitHub, GitLab, and Gitee
- **Intelligent Code Analysis**: AI-driven code structure analysis and cataloging
- **Model Context Protocol (MCP)**: Advanced AI interaction capabilities
- **RESTful API**: Comprehensive API for all operations
- **Multi-Database Support**: SQLite, PostgreSQL, MySQL support
- **Authentication & Authorization**: JWT-based security with role management
- **Background Processing**: Asynchronous task processing
- **Docker Ready**: Containerized deployment support

## Quick Start

### Prerequisites

- Python 3.11+
- Git
- Optional: Docker and Docker Compose

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd OpenDeepWiki/py
```

2. Create virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Configure environment:
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Run the application:
```bash
python src/main.py
```

The API will be available at `http://localhost:8000`

### Docker Deployment

1. Build and run with Docker Compose:
```bash
docker-compose up -d
```

2. Access the application:
- API: `http://localhost:8000`
- Documentation: `http://localhost:8000/api/docs`

## API Documentation

Once running, visit:
- Swagger UI: `http://localhost:8000/api/docs`
- ReDoc: `http://localhost:8000/api/redoc`

## Configuration

Key environment variables:

```env
# Database
DATABASE_URL=sqlite+aiosqlite:///./koalawiki.db

# AI Services
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key

# Git Integration
GITHUB_TOKEN=your-github-token
GITLAB_TOKEN=your-gitlab-token

# Security
SECRET_KEY=your-secret-key
JWT_EXPIRE_MINUTES=1440
```

## Architecture

### Core Components

- **FastAPI**: Modern, fast web framework
- **SQLAlchemy**: Async ORM for database operations
- **Pydantic**: Data validation and serialization
- **JWT Authentication**: Secure token-based auth
- **Background Tasks**: Async processing with asyncio

### Project Structure

```
py/src/
├── main.py                 # Application entry point
├── core/                   # Core configuration and utilities
│   ├── config.py          # Settings management
│   ├── database.py        # Database configuration
│   ├── auth.py            # Authentication utilities
│   └── middleware.py      # Custom middleware
├── models/                 # Database models
│   ├── user.py            # User and auth models
│   ├── warehouse.py       # Repository models
│   ├── document.py        # Document models
│   ├── mcp.py             # MCP models
│   └── system.py          # System models
├── schemas/                # Pydantic schemas
│   ├── auth.py            # Auth schemas
│   ├── warehouse.py       # Warehouse schemas
│   └── ...
├── api/                    # API routes
│   └── routers/           # API route modules
│       ├── auth.py        # Authentication endpoints
│       ├── warehouse.py   # Warehouse endpoints
│       └── ...
└── services/              # Business logic services
    ├── warehouse.py       # Warehouse service
    ├── document.py        # Document service
    └── ...
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user

### Warehouses
- `GET /api/warehouse/` - List warehouses
- `POST /api/warehouse/` - Create warehouse
- `GET /api/warehouse/{id}` - Get warehouse
- `PUT /api/warehouse/{id}` - Update warehouse
- `DELETE /api/warehouse/{id}` - Delete warehouse
- `POST /api/warehouse/{id}/sync` - Sync warehouse

### Documents
- `GET /api/document/` - List documents
- `GET /api/document/{id}` - Get document
- `GET /api/document/{id}/catalogs` - Get catalogs
- `POST /api/document/{id}/generate-catalog` - Generate AI catalog

### Users (Admin)
- `GET /api/user/` - List users
- `GET /api/user/{id}` - Get user
- `PUT /api/user/{id}` - Update user
- `DELETE /api/user/{id}` - Delete user

### System
- `GET /api/system/health` - Health check
- `GET /api/system/stats` - System statistics
- `GET /api/system/info` - System information

### MCP (Model Context Protocol)
- `GET /api/mcp/history` - Get chat history
- `POST /api/mcp/chat` - Send chat message
- `GET /api/mcp/todos` - Get agent todos
- `POST /api/mcp/todos` - Create todo
- `POST /api/mcp/tools/{tool}` - Execute tool

## Development

### Running Tests
```bash
pytest
```

### Code Formatting
```bash
black src/
isort src/
```

### Linting
```bash
flake8 src/
mypy src/
```

## Migration from .NET

This Python implementation provides equivalent functionality to the original .NET version:

### Migrated Components
- ✅ Web API with FastAPI
- ✅ Database models with SQLAlchemy
- ✅ Authentication & Authorization
- ✅ Warehouse management
- ✅ Document processing
- ✅ MCP integration
- ✅ Background services
- ✅ Multi-database support

### Key Differences
- **Framework**: ASP.NET Core → FastAPI
- **ORM**: Entity Framework → SQLAlchemy
- **Language**: C# → Python
- **Async**: Task/async → asyncio
- **DI**: Built-in DI → Dependency injection via FastAPI

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
