#!/bin/bash

# OpenDeepWiki Python API Startup Script

set -e

echo "Starting OpenDeepWiki Python API..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r ./requirements.txt

# Create necessary directories
echo "Creating directories..."
mkdir -p logs static uploads

# Check if .env exists
if [ ! -f "../.env" ]; then
    echo "Creating .env from template..."
    cp ../.env.example ../.env
    echo "Please edit .env file with your configuration before running again."
    exit 1
fi

# Run database migrations (if you add <PERSON><PERSON><PERSON> later)
# echo "Running database migrations..."
# alembic upgrade head

# Start the application
echo "Starting application..."
uvicorn src.main:app --host 0.0.0.0 --port 5085 --reload
